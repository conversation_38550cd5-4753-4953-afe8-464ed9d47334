// vite.config.ts
import legacy from '@vitejs/plugin-legacy'
import { defineConfig } from 'vite'
import { resolve } from 'path'

export default defineConfig(({ mode }) => ({
    base: mode === 'production' ? '/contract-review/' : './',

    // 路径别名配置
    resolve: {
        alias: {
            '@': resolve(__dirname, 'src'),
            '@/components': resolve(__dirname, 'src/component'),
            '@/lib': resolve(__dirname, 'src/lib'),
            '@/weboffice': resolve(__dirname, 'src/weboffice')
        }
    },
    plugins: [
        legacy({
            targets: ['defaults', 'ie >= 11', 'chrome 52'],
            additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
            renderLegacyChunks: true,
            polyfills: [
                'es.symbol',
                'es.array.filter',
                'es.array.flat',
                'es.promise',
                'es.promise.finally',
                'es/map',
                'es/set',
                'es.array.for-each',
                'es.object.define-properties',
                'es.object.define-property',
                'es.object.get-own-property-descriptor',
                'es.object.get-own-property-descriptors',
                'es.object.keys',
                'es.object.to-string',
                'web.dom-collections.for-each',
                'esnext.global-this',
                'esnext.string.match-all'
            ]
        }),
    ],
    server: {
        open: true,
        port: 3005,
        host: '0.0.0.0'
    },
    build: {
        // 资源优化
        assetsInlineLimit: 4096, // 小于4kb的资源内联
        cssCodeSplit: true,      // CSS代码分割
        sourcemap: mode !== 'production', // 生产环境关闭sourcemap

        // 压缩优化
        minify: mode === 'production' ? 'terser' : false,
        terserOptions: mode === 'production' ? {
            compress: {
                drop_console: true,  // 生产环境移除 console
                drop_debugger: true
            }
        } : {},

        rollupOptions: {
            output: {
                chunkFileNames: "contract-review/[name]-[hash].js",
                entryFileNames: "contract-review/[name]-[hash].js",
                assetFileNames: "contract-review/[name]-[hash].[ext]",

                // 代码分割
                manualChunks: {
                    // 第三方库单独打包
                    vendor: ['alpinejs', 'eventemitter3'],
                    // WPS Office 相关代码
                    weboffice: ['./src/weboffice/WebOfficeSDKWrapper.ts'],
                    // 业务组件
                    components: [
                        './src/component/ReviewDetailsComponent.ts',
                        './src/component/DOMUtils.ts'
                    ],
                    // 工具库
                    utils: ['./src/lib/ErrorHandler.ts']
                }
            }
        }
    }
}))
