
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width,user-scalable=no,initial-scale=1, minimum-scale=1,maximum-scale=1"/>
    <!-- force webkit on 360 -->
    <meta name="renderer" content="webkit"/>
    <meta name="force-rendering" content="webkit"/>
    <!-- force edge on IE -->
    <meta name="msapplication-tap-highlight" content="no"/>
    <!-- force full screen on some browser -->
    <meta name="full-screen" content="yes"/>
    <meta name="x5-fullscreen" content="true"/>
    <meta name="360-fullscreen" content="true"/>
    <!-- force screen orientation on some browser -->
    <meta name="screen-orientation" content=""/>
    <meta name="x5-orientation" content=""/>
    <title></title>
    <link rel="stylesheet" href="../static/fonts/bootstrap-icons/bootstrap-icons.min.css" />
    <link rel="stylesheet" href="../static/css/bootstrap.min.css" />
    <link rel="stylesheet" href="../static/css/style9027.css" />
    <link rel="stylesheet" href="../static/css/media9027.css" />
    <link rel="stylesheet" href="../static/css/baisheng.css" />
    <link rel="stylesheet" href="../static/css/form-elements.css" />
</head>
<body class="black" x-data="reviewDetailComponent">
<div id="main-wrapper">

    <div class="sidebar-wrapper">
        <div class="logo-gif"><img src=""/></div>
        <div class="sidebar-contract">

            <div class="company-tab dropdown">
                <a class="dropdown-toggle" data-toggle="dropdown"><i></i>上海立天唐人文化有限公司</a>
                <ul class="dropdown-menu public-dropdown-menu">
                    <li><a title="">苏州立天唐人文化有限公司</a></li>
                    <li><a title="">上海立天唐人文化有限公司</a></li>
                    <li><a title="">顶力立天唐人文化有限公司</a></li>
                </ul>
            </div>

            <div class="company-type">
                <ul>
                    <li>
                        <a class="item-type" title="苏州鑫心何商贸有限公司">
                            <label>苏州鑫心何商贸有限公司</label>
                            <i class="bi bi-chevron-down"></i>
                        </a>
                        <ul class="last-ul item-type-child" style="display: none;">
                            <li class="last-li">
                                <a title="租金结算通知单（样稿）"><i class="ca_bi bi-doc"></i><span>租金结算通知单（样稿）</span></a>
                            </li>
                        </ul>
                    </li>
                    <li>
                        <a class="item-type " title="上海鑫心何商贸有限公司"><label>上海鑫心何商贸有限公司</label><i class="bi bi-chevron-down"></i></a>
                        <ul class="last-ul item-type-child" style="display: none;">
                            <li class="last-li">
                                <a title="租金结算通知单（样稿）2222"><i class="ca_bi bi-doc"></i><span>租金结算通知单（样稿）</span></a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>

        </div>
        <div class="login-usershow">
            <i></i>张晓云
        </div>
    </div>

    <div class="main-content">
        <div class="main-process">

            <div class="wps-content" id="mainLeft">
                <iframe src="" width="100%" height="100%" frameborder="0" style="background: #eeeeee;"></iframe>
                <div class="prompt">
                    当前合同条款已修改，但未重新提交AI审核，目前AI审核风险尚未更新，请点击AI重审！
                    <button class="promptClose"></button>
                </div>
            </div>

            <div class="new-nestedDivider" id="nestedDivider"></div><!--收缩按钮-->

            <div class="audit-result" id="mainRight">
                <div class="tabs">
                    <ul>
                        <li class="on">审核结果</li>
                        <li>提取要素</li>
                    </ul>
                    <div class="operationGruop">
                        <button class="homeSearch"  data-toggle="dropdown" title="企业查询"></button>
                        <button class="yjb-btn"  data-toggle="tooltip" data-placement="top" title="上传意见表"></button>
                        <div class="dropdown">
                            <button class="down"  data-toggle="dropdown" title="下载"></button>
                            <ul class="dropdown-menu public-dropdown-menu">
                                <li><a href="#">下载清洁版</a></li>
                                <li><a href="#">下载简易版</a></li>
                            </ul>
                        </div>
                        <button class="bb-btn"  data-toggle="tooltip" data-placement="top" title="历史版本"></button>
                        <!-- <div class="dropdown">
          <button class="more-btn"   data-toggle="dropdown"  title="更多操作"></button>
          <ul class="dropdown-menu public-dropdown-menu">
            <li><a href="#">企业查询</a></li>
            <li><a href="#">历史版本</a></li>
            <li><a href="#">上传意见表</a></li>
          </ul>
        </div> -->
                    </div>
                </div>

                <!--企业查询-->
                <div class="businessSearch businessSearch-s">
                    <div class="public-black-header title">
                        <span>企业查询</span>
                        <div class="rightOperate">
                            <button class="oper-b oper-b3" data-toggle="tooltip" data-placement="top" title="关闭"></button>
                        </div>
                    </div>
                    <div class="poPupWindow">
                        <div class="business">
                            <input type="text" placeholder="请输入公司名称/工商注册地址"/>
                            <button>查询</button>
                        </div>
                    </div>
                </div>

                <!--审核结果-->
                <div class="tab-result" >
                    <div class="noResults noResults-new hide">
                        <div class="noResults-icon"></div>
                        <p>审核异常，无法显示审核结果</p>
                        <button class="toExamine">重新审核</button>
                    </div>
                    <div class="hasresult scrollable-container" >
                        <div class="search-condition">
                            <div class="ai-studio-list">
                                <div class="stu-1 "
                                     :class="{ active: currentRiskLevel.includes('1') }"
                                     @click="toggleRiskLevel('1')"
                                >
                                    <div x-text="getRiskCount('1')"></div>
                                    <p>重点关注</p>
                                </div>
                                <div class="stu-2"
                                     :class="{ active: currentRiskLevel.includes('2') }"
                                     @click="toggleRiskLevel('2')"
                                >
                                    <div x-text="getRiskCount('2')"></div>
                                    <p>关注</p>
                                </div>
                                <div class="stu-3"
                                     :class="{ active: currentRiskLevel.includes('3') }"
                                     @click="toggleRiskLevel('3')"
                                >
                                    <div x-text="getRiskCount('3')"></div>
                                    <p>提醒</p>
                                </div>
                                <div class="stu-4"
                                     :class="{ active: currentRiskLevel.includes('noRisk') }"
                                     @click="toggleRiskLevel('noRisk')"
                                >
                                    <div x-text="getRiskCount('noRisk')"></div>
                                    <p>无风险</p>
                                </div>
                            </div>
                            <div class="ai-studio-search">
                                <div class="s-search dropdown">
                                    <a class="dropdown-search" data-toggle="dropdown">
                                        <label x-text="ruleTypeOptions.find(opt => opt.value === currentRuleType)?.label || '全部'"></label>
                                    </a>
                                    <ul class="dropdown-menu public-dropdown-menu" >
                                        <template x-for="option in ruleTypeOptions" :key="option.value">
                                            <li>
                                                <a @click="currentRuleType = option.value" x-text="option.label"></a>
                                            </li>
                                        </template>
                                    </ul>
                                </div>
                                <div class="s-search dropdown">
                                    <a class="dropdown-search" data-toggle="dropdown">
                                        <label x-text="relationTypeOptions.find(opt => opt.value === currentRelationType)?.label || '全部'"></label>
                                    </a>
                                    <ul class="dropdown-menu public-dropdown-menu" >
                                        <template x-for="option in relationTypeOptions" :key="option.value">
                                            <li>
                                                <a @click="currentRelationType = option.value" x-text="option.label"></a>
                                            </li>
                                        </template>
                                    </ul>
                                </div>
                                <div class="s-search">
                                    <div class="s-search-input"><input type="text" x-model="currentSearchText" placeholder="搜索关键字"/></div>
                                </div>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                        <div class="ai-studio-result">
                            <ul>
                                <template x-for="item in filteredList()" :key="item.objectId">
                                    <li
                                            :class="[getRiskClass(item), (selectedItemId === item.objectId) ? 'active' : '']"
                                            @click="onItemClick(item)"
                                            @mouseover="item.showGiR = true;console.log('You double-clicked while holding Cmd/Meta key!')"
                                            @mouseout="item.showGiR = false"
                                    >
                                        <div class="studio-title">
                                            <div x-html="getTitleHtml(item)">

                                            </div>
                                            <template x-if="item.read">
                                                <i ></i>
                                            </template>
                                        </div>
                                        <!--        多条问题                                -->
                                        <div class="manyRisks">
                                            <div class="risksNums">合同内涉及此问题共有<span>3</span>处</div>
                                            <div class="risksbtns">
                                                <button class="preBtn" disabled></button>
                                                <span>1/3</span>
                                                <button class="nextBtn"></button>
                                            </div>
                                        </div>
                                        <div class="studio-details">
                                            <template x-if="item.atts && item.atts.length > 0">
                                                <div class="gi">
                                                    <label>相关附件</label>
                                                    <div class="gi-fjlist">
                                                        <template x-for="att in item.atts" :key="att.objectId">
                                                            <a href="#" @click="onAttachmentClick(item.objectId,att)"><u x-text="att.name"></u></a>
                                                        </template>
                                                    </div>
                                                </div>
                                            </template>
                                            <template x-if="item.riskCause">
                                                <div class="gi gi2">
                                                    <label>提示原因</label>
                                                    <div class="gi-fjlist" x-text="item.riskCause">
                                                        上手租约起止时间未知，转租/授权链条期限
                                                    </div>
                                                </div>
                                            </template>
                                            <template x-if="item.semanticDiffList && item.semanticDiffList.length > 0">
                                                <div class="gi gi2">
                                                    <label>语义差异</label>
                                                    <div class="gi-fjlist yy-difference">
                                                        <ol>
                                                            <template x-for="diff in item.semanticDiffList" :key="diff">
                                                                <li x-text="diff"></li>
                                                            </template>
                                                        </ol>
                                                    </div>
                                                </div>
                                            </template>
                                            <template x-if="item.additionalInfo">
                                                <div class="gi gi2">
                                                    <label>附加信息</label>
                                                    <div class="gi-fjlist "
                                                         :class="getAdditionalInfoClass(item.additionalInfo)"
                                                         x-text="item.additionalInfo"
                                                    ></div>
                                                </div>
                                            </template>
                                            <template x-if="!item.showRemarkInput">
                                                <div class="gi gi2 gi-icon">
                                                    <div class="gi-d">
                                                        <template x-if="!item.ignore">
                                                            <a @click="onRejectClick(item)" class="b-icon" data-toggle="tooltip" data-placement="top" title="忽略此风险"><i class="ignore"></i></a>
                                                        </template>
                                                        <template x-if="item.ignore">
                                                            <a @click="onReopenClick(item)" class="b-icon" data-toggle="tooltip" data-placement="top" title="撤回忽略"><i class="withdraw"></i></a>
                                                        </template>
                                                        <a @click="onRemarkClick(item)" class="b-icon" data-toggle="tooltip" data-placement="top" title="备注"><i class="notes"></i></a>
                                                    </div>
                                                    <div class="gi-r" x-show="item.showGiR">
                                                        <a class="dz" @click="onLikeClick(item.objectId)" ></a>
                                                        <a class="dc" @click="onDislikeClick(item.objectId)"></a>
                                                    </div>
                                                </div>
                                            </template>
                                            <template x-if="item.showRemarkInput">
                                                <div class="gi gi-input">
                                                    <input type="text" x-model="item.newRemarkContent" placeholder="请填写备注" class="bz-input"/>
                                                    <div class="ip-btngroup">
                                                        <button type="button" @click="event.stopPropagation();submitRemark(item)"
                                                                class="colored"
                                                                :class="{'disabled': item.newRemarkContent.trim() === ''}"
                                                        >确定</button>
                                                        <button @click="event.stopPropagation();resetRemark(item)">取消</button>
                                                    </div>
                                                </div>
                                            </template>
                                            <template x-if="item.remarks && item.remarks.length > 0">
                                                <div @click="onShowRemarks(item.objectId,item.remarks,$event)" class="gi_bz" data-toggle="tooltip" data-placement="top" title="查看全部日志">
                                                    <i class="get-notes"></i>
                                                    <label class="get-label" x-text="getRemarkType(item.remarks[0].type)">忽略</label>
                                                    <div class="bzmain" x-text="`${item.remarks[0].userName}：${item.remarks[0].remark}`">张三：这不是风险，误报</div>
                                                    <div class="bznums">
                                                        <div x-text="`共${item.remarks.length}条`">共1条</div>
                                                        <i></i>
                                                    </div>
                                                </div>
                                            </template>
                                        </div>
                                    </li>
                                </template>
                            </ul>
                        </div>
                        <div class="ai-studio-bottom">
                            <div class="read">
                                <button><i></i>全部已读</button>
                                <span>未读 193</span>
                            </div>
                            <div class="otherBtns">
                                <button class="save">保存</button><!-- <button class="save" disabled>保存</button> -->
                                <button class="toExamine">审核</button><!-- <button class="toExamine" disabled>审核</button> -->
                            </div>
                        </div>
                    </div>

                </div>

                <!--提取要素-->
                <div class="tab-result tab-partresult hide">
                    <table class="table offcanvas-body-table">
                        <thead>
                        <tr>
                            <th style="width:40%;">要素名称</th>
                            <th>提取结果</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>承租方（乙方）</td>
                            <td>
                                <div class="haslink">
                                    <span>上海必胜客有限公司</span>
                                    <a href="#" target="_blank" class="qc">企查 &gt;</a>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>承租方地址</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>承租方授权代表</td>
                            <td>范军</td>
                        </tr>
                        <tr>
                            <td>出租方（甲方）</td>
                            <td>融通地产（上海）有限责任公司</td>
                        </tr>
                        <tr>
                            <td>省份</td>
                            <td>上海市</td>
                        </tr>
                        <tr>
                            <td>城市</td>
                            <td>上海市</td>
                        </tr>
                        <tr>
                            <td>地址</td>
                            <td>
                                <div class="haslink">
                                    <span>上海市广粤路299号1层124室、126室</span>
                                    <a href="#" target="_blank" class="qc">查找 &gt;</a>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>

            </div>

        </div>
    </div>

</div>

<div id="remarksWin" class="popWindow popWindow-content popWindow-rz">
    <div class="public-black-header title">
        <span>日志记录</span>
        <div class="rightOperate">
            <button class="oper-b oper-b3"></button>
        </div>
    </div>
    <div class="poPupWindow" >
        <ul class="recording"  >
            <template x-for="(remark, index) in currentRemarks" :key="index">
                <li>
                    <div class="resTop">
                        <label x-text="getRemarkType(remark.type)">撤回忽略</label>
                        <span x-text="remark.remark" >是风险，请严谨判断</span>
                    </div>
                    <div class="resBot">
                        <span x-text="remark.userName">李四</span>
                        <span x-text="formatDate(remark.createdAt)">2024.12.11 10:30</span>
                    </div>
                </li>
            </template>
        </ul>
    </div>
    <div class="clearfix"></div>
    <div class="resizer" style="display: none;"></div>

</div>


<script>

    $(function() {
        $('[data-toggle="tooltip"]').tooltip();

        // 封装显示/隐藏菜单的函数
        function toggleMenu(link) {
            var menu = link.nextElementSibling;
            var parentLi = link.closest('li'); // 获取父 <li> 元素

            // 获取所有的父容器 li 元素
            var allLi = document.querySelectorAll('li');

            // 遍历所有的 <li>，关闭其他展开的菜单
            for (var i = 0; i < allLi.length; i++) {
                var li = allLi[i];
                var otherMenu = li.querySelector('.last-ul');
                if (otherMenu && otherMenu !== menu) {
                    otherMenu.style.display = 'none'; // 隐藏其他菜单
                    li.classList.remove('open'); // 去除 open 样式
                }
            }

            // 切换当前菜单的显示状态
            if (menu.style.display === 'none' || menu.style.display === '') {
                menu.style.display = 'block'; // 显示当前菜单
                parentLi.classList.add('open'); // 给父 <li> 添加 open 样式
            } else {
                menu.style.display = 'none'; // 隐藏当前菜单
                parentLi.classList.remove('open'); // 去除父 <li> 的 open 样式
            }
        }

        // 获取所有的 item-type 链接，并绑定事件
        function setupToggleMenus() {
            var itemLinks = document.querySelectorAll('.item-type');
            for (var i = 0; i < itemLinks.length; i++) {
                var link = itemLinks[i];
                link.addEventListener('click', function(event) {
                    event.preventDefault(); // 阻止默认行为
                    toggleMenu(this); // 调用封装的函数
                });
            }
        }

        setupToggleMenus();

        // $(".item-type").bind("click", function() {
        // 	var parent = $(this).parent();
        //     var sub = parent.find("> ul");
        //          if(sub.is(":visible")) {
        //                parent.removeClass("open");
        //          } else {
        //             visibleSubMenuClose();
        //             parent.addClass("open");
        //          }
        // });
        //
        //  function visibleSubMenuClose() {
        //    $(".company-type>ul>li").each(function() {
        //       var t = $(this);
        //       if(t.hasClass("open")) {
        //               t.removeClass("open");
        //       }
        //    });
        //  }
        // $('.collapsible').bind("click", function() {
        // 	var parent = $(this).parent();
        // 	var sub = parent.find("> ul");
        // 	     if(sub.is(":visible")) {
        // 	           parent.removeClass("open");
        // 	     } else {
        // 	        visibleSubMenuClose2();
        // 	        parent.addClass("open");
        // 	     }
        //  });
        //
        // function visibleSubMenuClose2() {
        //   $(".item-type-child>li").each(function() {
        //      var t = $(this);
        //      if(t.hasClass("open")) {
        //              t.removeClass("open");
        //      }
        //   });
        // }

        //审核结果，提取要素切换
        $(".tabs>ul>li").bind("click", function() {
            var _index = $(this).index();
            $(this).addClass("on").siblings().removeClass("on");
            $(".tab-result").addClass("hide");
            $(".tab-result").eq(_index).removeClass("hide");
        });

        //企业查询
        $(".homeSearch").click(function(){
            $(".businessSearch-s").show();
            $(this).addClass("on");
            return false;
        });
        $(document).click(function(event) {
            if(!$(event.target).closest(".businessSearch-s").length){
                $(".businessSearch-s").hide();
                $(".homeSearch").removeClass("on");
            }
        });
        $(".businessSearch-s").click(function(event) {
            event.stopPropagation();
        });

        $(".businessSearch-s .oper-b3").click(function(){
            $(".businessSearch-s").hide();
            $(".homeSearch").removeClass("on");
        });

        //显示点赞
        // $(".ai-studio-result>ul>li").hover(function(){
        // 	  $(this).find(".gi-r").show();
        // 		}, function() {
        // 		$(this).find(".gi-r").hide();
        // });

        // $(".bz-input").on('input', function() {
        //   var inputVal = $(this).val();
        //   if (inputVal.trim() !== '') {
        //     $(".colored").removeClass('disabled');
        //   } else {
        //     $(".colored").addClass('disabled');
        //   }
        // });

        //
        // 监听显示备注历史窗口事件


        window.addEventListener('show-remark-click', (event) => {
            console.log('Remark submitted:', event.detail);
            var popupElement=document.getElementById('remarksWin');
            var liElement=event.detail.liElement;
            // 每次点击时都重新计算弹窗的位置
            setTimeout(() => {
                var position=calculatePopupPosition(liElement, popupElement);
                console.log(position);
                //popupElement.style.display = "block"; // 显示弹窗
            }, 0);
        });

        function calculatePopupPosition(liElement, popupElement) {
            if (!liElement || !popupElement) {
                console.error("Invalid arguments: liElement, popupElement are required.");
                return;
            }



            // 获取 <li> 元素的位置和尺寸
            const liRect = liElement.getBoundingClientRect();
            console.log(liRect);

            // 获取弹出窗口的高度
            popupElement.style.position = "absolute";
            popupElement.style.visibility = "hidden"; // 临时显示以获取高度
            popupElement.style.display = "block";

            const popupHeight = popupElement.offsetHeight;
            const popupWidth = popupElement.offsetWidth;

            // 默认弹出窗口显示在 <li> 的底部左侧
            let top = liRect.bottom
            let left = liRect.left + window.scrollX;

            // 获取视口的高度
            const viewportHeight = window.innerHeight;
            // 计算窗口是否会超出视口的底部
            if (top + popupHeight > viewportHeight + window.scrollY) {
                // 如果窗口超出底部，设置弹出窗口在 <li> 的上方
                top = liRect.top- popupHeight;
            }

            // 更新弹出窗口的位置
            // popupElement.style.top = `${top}px`;
            // popupElement.style.left = `${left}px`;

            // 恢复弹窗的显示
            popupElement.style.visibility = "visible";
            popupElement.style.display = "block";  // 使弹窗可见
            return { y:top, x:left };
        }
        // 示例附件数据
        const attachments = [
            { objectId: "att1", name: "附件1.pdf" },
            { objectId: "att2", name: "附件2.docx" },
        ];

        // 示例备注数据
        const remarks = [
            {
                userName: "用户A",
                type: 1,
                remark: "已经阅读内容",
                createdAt: Date.now() - 1000000,
            },
            {
                userName: "用户B",
                type: 2,
                remark: "忽略无关信息",
                createdAt: Date.now() - 500000,
            },
        ];

        // 示例审核数据
        var reviewData = [
            {
                objectId: "1",
                ruleType: "FW",
                isRisk: true,
                atts: [
                    { objectId: "att1", name: "合同附件1" },
                    { objectId: "att2", name: "合同附件2" },
                ],
                relationType: 1,
                riskLevel: "1",
                additionalInfo: "需要注意的风险点",
                riskMessage: "风险提示消息",
                riskCause: "风险原因描述",
                semanticDiffList: ["语义差异1", "语义差异2"],
                ignore: false,
                remarks: remarks,
                attObjectId: "",
                showRemarkInput: false,
                newRemarkContent: "",
                remarkStatus: 1,
            },
            {
                objectId: "2",
                ruleType: "CW",
                isRisk: true,
                atts: [
                    { objectId: "att3", name: "财务报表附件" },
                ],
                relationType: 2,
                riskLevel: "2",
                additionalInfo: "中等风险的点",
                riskMessage: "中风险提示",
                riskCause: "中风险原因",
                semanticDiffList: [],
                ignore: false,
                remarks: [],
                attObjectId: "",
                showRemarkInput: false,
                newRemarkContent: "",
                remarkStatus: 1,
            },
            {
                objectId: "3",
                ruleType: "YW",
                isRisk: true,
                atts: [],
                relationType: 3,
                riskLevel: "3",
                additionalInfo: "高风险点",
                riskMessage: "高风险提示",
                riskCause: "高风险原因",
                semanticDiffList: ["差异描述1"],
                ignore: false,
                remarks: remarks,
                attObjectId: "",
                showRemarkInput: false,
                newRemarkContent: "",
                remarkStatus: 1,
            },
        ];
        var reviewList={
            list:reviewData
        }

        var reviewDetail = new ReviewDetail(reviewList);




    });

</script>
</body>
</html>
