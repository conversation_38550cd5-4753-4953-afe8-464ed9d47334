{"name": "daorigin_wps_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:dev": "tsc && vite build --mode development", "preview": "vite preview"}, "devDependencies": {"@rollup/plugin-inject": "^5.0.5", "@types/alpinejs": "^3.13.10", "@vitejs/plugin-legacy": "^5.4.0", "typescript": "^5.2.2", "vite": "5.4.6"}, "dependencies": {"@types/crypto-js": "^4.2.2", "alpinejs": "^3.14.3", "eventemitter3": "^5.0.1"}}