{"name": "daorigin_wps_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build --mode production", "build:dev": "tsc && vite build --mode development", "build:analyze": "tsc && vite build --mode production && npx vite-bundle-analyzer dist", "preview": "vite preview", "type-check": "tsc --noEmit"}, "devDependencies": {"@rollup/plugin-inject": "^5.0.5", "@types/alpinejs": "^3.13.10", "@vitejs/plugin-legacy": "^5.4.0", "typescript": "^5.2.2", "vite": "5.4.6"}, "dependencies": {"alpinejs": "^3.14.3", "eventemitter3": "^5.0.1"}}