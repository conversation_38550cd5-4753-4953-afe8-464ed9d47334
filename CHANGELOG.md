# 更新日志

本文档记录了项目的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增
- 项目文档完整化
- API 文档和开发指南
- 部署文档和配置示例

### 变更
- 优化项目结构说明
- 完善组件使用示例

## [0.0.0] - 2024-01-01

### 新增
- 项目初始化
- TypeScript + Vite 构建配置
- Alpine.js 集成
- WPS Office SDK 集成

#### 核心功能
- **WebOffice 集成**
  - WPS SDK 封装器 (`WebOfficeSDKWrapper`)
  - 文档在线编辑和查看
  - 书签管理功能
  - 评论系统
  - 修订模式支持

- **财务系统管理**
  - 财务系统组件 (`FinancialSystemCom`)
  - 餐厅基础信息管理
  - BU Level 信息录入
  - 租赁合同管理
  - 表单验证和数据绑定

- **合同审核系统**
  - 审核详情组件 (`ReviewDetailsComponent`)
  - 合同条款审核
  - 风险等级评估和筛选
  - 审核意见和备注管理
  - 状态跟踪和更新

- **租赁管理**
  - 租赁组件 (`LeasesComponent`)
  - 租赁详情管理
  - 动态表单处理

#### 技术架构
- **组件系统**
  - Alpine.js 响应式数据系统
  - 组件生命周期管理
  - 事件系统集成

- **类型系统**
  - TypeScript 类型定义
  - 财务数据类型 (`financial.ts`)
  - WPS SDK 类型定义

- **构建系统**
  - Vite 5.4.6 构建工具
  - Legacy 浏览器支持 (IE 11+)
  - 代码分割和优化
  - 开发服务器配置

#### 用户界面
- **主页面** (`index.html`)
  - WPS 文档查看器集成
  - 合同附件管理
  - 双栏布局设计
  - 响应式界面

- **财务页面** (`财务.html`)
  - 财务信息表单
  - 动态内容展开/收起
  - 数据验证和提交

- **样式系统**
  - 全局样式定义
  - 组件样式封装
  - 动画和过渡效果

#### 开发工具
- **包管理**
  - pnpm 包管理器
  - 依赖锁定文件
  - 脚本命令配置

- **开发环境**
  - 热重载开发服务器
  - TypeScript 编译配置
  - 源码映射支持

### 依赖项
- **核心依赖**
  - `alpinejs@^3.14.3` - 响应式 UI 框架
  - `eventemitter3@^5.0.1` - 事件系统

- **开发依赖**
  - `typescript@^5.2.2` - TypeScript 编译器
  - `vite@5.4.6` - 构建工具
  - `@vitejs/plugin-legacy@^5.4.0` - 浏览器兼容性
  - `@rollup/plugin-inject@^5.0.5` - 模块注入
  - `@types/alpinejs@^3.13.10` - Alpine.js 类型定义

### 配置文件
- **TypeScript 配置** (`tsconfig.json`)
  - ES2020 目标版本
  - 严格模式启用
  - DOM 类型支持
  - 模块解析配置

- **Vite 配置** (`vite.config.ts`)
  - 相对路径基础配置
  - Legacy 浏览器支持
  - 开发服务器设置
  - 构建优化配置

- **包配置** (`package.json`)
  - 项目元信息
  - 脚本命令定义
  - 依赖版本管理

### 项目结构
```
daorigin_wps_ts/
├── src/
│   ├── component/          # 业务组件
│   ├── lib/               # 第三方库和工具
│   ├── weboffice/         # WPS 集成
│   └── main.ts            # 应用入口
├── public/                # 静态资源
├── dist/                  # 构建输出
├── docs/                  # 项目文档
├── index.html             # 主页面
└── 配置文件...
```

### 浏览器支持
- Chrome 52+
- Firefox 最新版本
- Safari 最新版本
- Edge 最新版本
- Internet Explorer 11+

### 已知问题
- WPS SDK 需要 HTTPS 环境才能正常工作
- IE 11 需要额外的 polyfill 支持
- 某些 Alpine.js 功能在旧版浏览器中可能有兼容性问题

### 安全考虑
- 所有外部资源通过 HTTPS 加载
- 实施了基本的 XSS 防护
- 用户输入进行了基础验证

---

## 版本说明

### 版本号规则
- **主版本号**: 不兼容的 API 修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 变更类型
- `新增` - 新功能
- `变更` - 对现有功能的变更
- `废弃` - 即将移除的功能
- `移除` - 已移除的功能
- `修复` - 问题修复
- `安全` - 安全相关修复

### 贡献指南
1. 所有重要变更都应该记录在此文档中
2. 按照时间倒序排列版本
3. 使用清晰的分类和描述
4. 包含影响用户的所有变更
5. 提供必要的迁移指南

### 发布流程
1. 更新版本号 (`package.json`)
2. 更新变更日志 (`CHANGELOG.md`)
3. 创建 Git 标签
4. 构建和测试
5. 部署到生产环境
6. 发布版本说明
