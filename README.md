# DaOrigin WPS TypeScript 项目

## 项目概述
百胜项目 9027  wps 合同审核前端项目
DaOrigin WPS TypeScript 是一个基于 TypeScript 和 Vite 构建的 Web 应用程序，主要用于集成 WPS Office 在线文档编辑功能， 合同审核管理功能。

## 技术栈

- **前端框架**: TypeScript + Vite
- **UI 框架**: Alpine.js
- **构建工具**: Vite 5.4.6
- **包管理器**: pnpm
- **兼容性**: 支持 IE 11+ 和现代浏览器
- **文档集成**: WPS Office SDK

## 项目结构

```
daorigin_wps_ts/
├── src/                          # 源代码目录
│   ├── component/                # 组件目录
│   │   ├── DOMUtils.ts           # DOM 工具类
│   │   └── ReviewDetailsComponent.ts # 审核详情组件
│   ├── lib/                      # 第三方库和工具
│   │   ├── ErrorHandler.ts      # 错误处理工具
│   │   └── weboffice/           # WPS Office SDK
│   ├── weboffice/               # WPS Office 集成
│   │   └── WebOfficeSDKWrapper.ts  # WPS SDK 封装器
│   ├── main.ts                  # 应用入口文件
│   └── style.css               # 全局样式
├── public/                      # 静态资源
├── dist/                        # 构建输出目录
├── docs/                        # 项目文档
├── index.html                   # 主页面
├── package.json                 # 项目配置
├── tsconfig.json               # TypeScript 配置
└── vite.config.ts              # Vite 配置
```

## 核心功能

### 1. WPS Office 集成
- 在线文档编辑和查看
- 支持 Word、Excel、PowerPoint 等格式
- 文档修订和批注功能
- 书签和评论管理

### 2. 合同审核系统
- 合同条款审核
- 风险等级评估
- 审核意见和备注
- 状态跟踪和管理

## 安装和运行

### 环境要求
- Node.js >= 16.0.0
- pnpm >= 7.0.0

### 安装依赖
```bash
pnpm install
```

### 开发模式
```bash
pnpm dev
```
应用将在 http://localhost:3005 启动

### 构建项目
```bash
# 生产环境构建
pnpm build

# 开发环境构建
pnpm build:dev
```

### 预览构建结果
```bash
pnpm preview
```

## 配置说明

### Vite 配置
- 基础路径设置为相对路径 `./`
- 支持 IE 11+ 的 Legacy 插件
- 开发服务器端口: 3005
- 构建输出优化配置

### TypeScript 配置
- 目标版本: ES2020
- 模块系统: ESNext
- 严格模式启用
- 支持 DOM 类型

## 主要组件说明

### WebOfficeSDKWrapper
WPS Office SDK 的封装类，提供：
- 文档初始化和配置
- 书签管理
- 评论系统
- 修订模式控制

### ReviewDetailsComponent
审核详情组件，提供：
- 审核列表管理
- 风险等级筛选
- 搜索和过滤
- 状态更新

## 开发指南

### 添加新组件
1. 在 `src/component/` 目录下创建新组件
2. 使用 Alpine.js 进行数据绑定
3. 在 `main.ts` 中导入和暴露

### 集成 WPS Office
1. 配置 WPS SDK 参数
2. 使用 `WebOfficeSDKWrapper` 初始化
3. 监听文档事件
4. 处理用户交互

## 浏览器兼容性

- Chrome 52+
- Firefox 最新版本
- Safari 最新版本
- Edge 最新版本
- Internet Explorer 11+

## 许可证

本项目为私有项目，版权所有。

## 联系方式

如有问题或建议，请联系开发团队。
