# 开发文档

## 项目架构

### 整体架构
本项目采用模块化架构，主要分为以下几个层次：

1. **表现层 (Presentation Layer)**: HTML 模板 + Alpine.js 数据绑定
2. **业务逻辑层 (Business Logic Layer)**: TypeScript 组件类
3. **数据访问层 (Data Access Layer)**: WPS SDK 和数据模型
4. **基础设施层 (Infrastructure Layer)**: 工具类和配置

### 核心设计模式

#### 1. 组件模式 (Component Pattern)
所有业务组件都继承自 `BaseComponent` 基类：

```typescript
export abstract class BaseComponent<T> {
    protected data: T;
    protected isVisible: boolean;
    
    constructor(data: T) {
        this.data = data;
        this.isVisible = false;
    }
    
    abstract init(): void;
    abstract validateForm(): boolean;
}
```

#### 2. 管理器模式 (Manager Pattern)
`AlpineManager` 负责管理所有 Alpine.js 组件的注册和生命周期：

```typescript
export class AlpineManager {
    private static components: Map<string, Function> = new Map();
    
    static registerComponent(name: string, component: Function) {
        this.components.set(name, component);
        Alpine.data(name, component);
    }
    
    static start() {
        Alpine.start();
    }
}
```

#### 3. 包装器模式 (Wrapper Pattern)
`WebOfficeSDKWrapper` 封装了 WPS SDK 的复杂性：

```typescript
export default class WebOfficeSDKWrapper {
    private instance: IWps;
    private app: any;
    private eventEmitter: EventEmitter;
    
    constructor(config: WebOfficeConfig) {
        this.instance = OpenSDK.config(config);
        this.eventEmitter = new EventEmitter();
    }
}
```

## 组件开发指南

### 创建新组件

1. **创建组件类**
```typescript
// src/component/MyComponent.ts
import { BaseComponent } from './BaseComponent';
import { AlpineManager } from './AlpineManager';

export class MyComponent extends BaseComponent<MyDataType> {
    constructor(data: MyDataType) {
        super(data);
        this.registerComponent();
    }
    
    init(): void {
        // 初始化逻辑
    }
    
    validateForm(): boolean {
        // 表单验证逻辑
        return true;
    }
    
    private registerComponent() {
        AlpineManager.registerComponent("myComponent", () => ({
            data: this.data,
            isVisible: this.isVisible,
            init: () => this.init(),
            // 其他方法...
        }));
    }
}
```

2. **定义数据类型**
```typescript
// src/types/my-types.ts
export interface MyDataType {
    id: string;
    name: string;
    // 其他属性...
}
```

3. **在 main.ts 中注册**
```typescript
import { MyComponent } from "./component/MyComponent";
(<any>window).MyComponent = MyComponent;
```

### Alpine.js 集成

#### 数据绑定
```html
<div x-data="myComponent" x-init="init()">
    <input x-model="data.name" type="text" />
    <button x-on:click="validateForm()">验证</button>
</div>
```

#### 事件处理
```typescript
// 在组件中定义事件处理方法
handleClick(event: Event) {
    event.preventDefault();
    // 处理逻辑
}

// 在 Alpine.js 组件中绑定
AlpineManager.registerComponent("myComponent", () => ({
    handleClick: (event: Event) => this.handleClick(event)
}));
```

## WPS Office 集成

### 初始化 WPS SDK

```typescript
const wrapper = new WebOfficeSDKWrapper({
    url: 'https://your-document-url.com/doc.docx',
    token: 'your-access-token',
    mountId: 'wps-container',
    officeType: 'word'
});

await wrapper.init();
```

### 文档操作

#### 书签管理
```typescript
// 获取所有书签
const bookmarks = await wrapper.getBookmarks();

// 跳转到书签
await wrapper.gotoBookmark('bookmark-name');

// 获取书签内容
const content = await wrapper.getBookmarkText('bookmark-name');
```

#### 评论系统
```typescript
// 获取所有评论
const comments = await wrapper.getComments();

// 添加评论
await wrapper.addComment('评论内容', position);

// 删除评论
await wrapper.deleteComment(commentId);
```

### 事件监听

```typescript
wrapper.on('documentReady', () => {
    console.log('文档已准备就绪');
});

wrapper.on('contentChanged', (data) => {
    console.log('文档内容已更改', data);
});
```

## 数据流管理

### 状态管理
项目使用 Alpine.js 的响应式系统进行状态管理：

```typescript
// 组件内部状态
export class ReviewDetailsComponent extends BaseComponent<ReviewData> {
    updateData(newData: Partial<ReviewData>) {
        Object.assign(this.data, newData);
        // Alpine.js 会自动检测变化并更新 DOM
    }
}
```

### 组件间通信
使用事件系统进行组件间通信：

```typescript
// 发送事件
this.eventEmitter.emit('dataUpdated', { id: '123', data: newData });

// 监听事件
this.eventEmitter.on('dataUpdated', (payload) => {
    this.handleDataUpdate(payload);
});
```

## 样式和主题

### CSS 组织
- 全局样式：`src/style.css`
- 组件样式：内联在 HTML 模板中
- 主题变量：使用 CSS 自定义属性

### 响应式设计
```css
/* 移动端适配 */
@media (max-width: 768px) {
    .finance-wrapper {
        flex-direction: column;
    }
}
```

## 测试策略

### 单元测试
```typescript
// 测试组件方法
describe('FinancialSystemCom', () => {
    it('should validate form correctly', () => {
        const component = new FinancialSystemCom(testData);
        expect(component.validateForm()).toBe(true);
    });
});
```

### 集成测试
```typescript
// 测试 WPS SDK 集成
describe('WebOfficeSDKWrapper', () => {
    it('should initialize correctly', async () => {
        const wrapper = new WebOfficeSDKWrapper(config);
        await wrapper.init();
        expect(wrapper.isReady()).toBe(true);
    });
});
```

## 性能优化

### 代码分割
```typescript
// 动态导入大型组件
const loadFinancialComponent = async () => {
    const { FinancialSystemCom } = await import('./component/FinancialSystemCom');
    return FinancialSystemCom;
};
```

### 懒加载
```typescript
// 延迟初始化 WPS SDK
const initWPSWhenNeeded = () => {
    if (!wpsInitialized) {
        return import('./weboffice/WebOfficeSDKWrapper').then(module => {
            return new module.default(config);
        });
    }
};
```

## 调试和故障排除

### 开发工具
- 使用 `debug: true` 启用 WPS SDK 调试模式
- 浏览器开发者工具进行 DOM 调试
- Alpine.js DevTools 扩展

### 常见问题
1. **WPS SDK 初始化失败**: 检查 token 和 URL 配置
2. **Alpine.js 数据绑定不工作**: 确保组件正确注册
3. **样式不生效**: 检查 CSS 选择器优先级

## 部署指南

### 构建优化
```bash
# 生产环境构建
pnpm build

# 分析构建包大小
pnpm build --analyze
```

### 环境配置
```typescript
// vite.config.ts
export default defineConfig({
    base: process.env.NODE_ENV === 'production' ? '/app/' : './',
    build: {
        sourcemap: process.env.NODE_ENV !== 'production'
    }
});
```
