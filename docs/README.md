# 项目文档

欢迎来到 DaOrigin WPS TypeScript 项目文档中心。这里包含了项目的完整文档，帮助您快速了解和使用本项目。

## 📚 文档导航

### 🚀 快速开始
- [项目概述](../README.md) - 项目介绍、技术栈和基本信息
- [安装指南](../README.md#安装和运行) - 环境配置和项目启动

### 🛠️ 开发文档
- [开发指南](DEVELOPMENT.md) - 详细的开发文档和架构说明
- [API 文档](API.md) - 完整的 API 接口文档
- [组件文档](#组件文档) - 各个组件的使用说明

### 🚀 部署文档
- [部署指南](DEPLOYMENT.md) - 生产环境部署配置
- [环境配置](DEPLOYMENT.md#环境变量配置) - 环境变量和配置说明

### 📋 项目管理
- [更新日志](../CHANGELOG.md) - 版本更新记录
- [问题排查](#问题排查) - 常见问题和解决方案

## 🏗️ 项目架构

### 技术栈
- **前端**: TypeScript + Vite + Alpine.js
- **文档集成**: WPS Office SDK
- **构建工具**: Vite 5.4.6
- **包管理**: pnpm

### 核心模块
```
src/
├── component/          # 业务组件
│   ├── DOMUtils.ts           # DOM 工具类
│   └── ReviewDetailsComponent.ts # 审核详情组件
├── lib/               # 第三方库和工具
│   ├── ErrorHandler.ts      # 错误处理工具
│   └── weboffice/           # WPS Office SDK
├── weboffice/         # WPS Office 集成
│   └── WebOfficeSDKWrapper.ts   # WPS SDK 封装器
├── main.ts            # 应用入口文件
└── style.css         # 全局样式
```

## 📖 组件文档

### WebOffice 集成组件

#### WebOfficeSDKWrapper
WPS Office SDK 的核心封装类，提供文档编辑和管理功能。

**主要功能:**
- 文档在线编辑和查看
- 书签管理和导航
- 评论系统
- 修订模式控制

**使用示例:**
```typescript
const wrapper = new WebOfficeSDKWrapper({
    url: 'https://your-document-url.com/doc.docx',
    token: 'your-access-token',
    mountId: 'wps-container'
});

await wrapper.init();
```

详细 API 请参考 [API 文档](API.md#webofficesdk-wrapper-api)。

### 业务组件

#### ReviewDetailsComponent
合同审核详情组件，提供审核流程管理。

**主要功能:**
- 合同条款审核
- 风险等级评估
- 审核意见管理
- 状态跟踪

**使用示例:**
```typescript
const reviewDetail = new ReviewDetail(reviewData);
```



## 🔧 开发工具

### 开发命令
```bash
# 启动开发服务器
pnpm dev

# 构建项目
pnpm build

# 预览构建结果
pnpm preview
```

### 代码规范
- 使用 TypeScript 进行类型检查
- 遵循 ESLint 代码规范
- 使用 Prettier 格式化代码

### 调试工具
- 浏览器开发者工具
- Alpine.js DevTools 扩展
- WPS SDK 调试模式

## 🌐 浏览器支持

| 浏览器 | 版本要求 |
|--------|----------|
| Chrome | 52+ |
| Firefox | 最新版本 |
| Safari | 最新版本 |
| Edge | 最新版本 |
| IE | 11+ |

## 🔒 安全考虑

### HTTPS 要求
WPS SDK 要求在 HTTPS 环境下运行，本地开发时可以使用自签名证书。

### 内容安全策略
项目实施了基本的 CSP 策略，限制外部资源加载。

### 数据验证
所有用户输入都进行了基础验证，防止 XSS 攻击。

## ❓ 问题排查

### 常见问题

#### WPS SDK 初始化失败
**症状**: 文档无法加载或显示错误
**解决方案**:
1. 检查是否在 HTTPS 环境下运行
2. 验证 App ID 和 Token 是否正确
3. 确认网络连接正常

#### Alpine.js 数据绑定不工作
**症状**: 页面交互无响应
**解决方案**:
1. 检查组件是否正确注册
2. 验证 Alpine.js 是否正确初始化
3. 查看浏览器控制台错误信息

#### 构建失败
**症状**: 运行 `pnpm build` 时出错
**解决方案**:
1. 清除 node_modules 重新安装依赖
2. 检查 TypeScript 类型错误
3. 验证 Vite 配置是否正确

### 调试技巧

#### 启用调试模式
```typescript
// 在 WebOffice 配置中启用调试
const config = {
    debug: true,
    // 其他配置...
};
```

#### 查看详细日志
```bash
# 启动开发服务器时显示详细日志
pnpm dev --debug
```

#### 分析构建包大小
```bash
# 分析构建结果
pnpm build --analyze
```

## 🤝 贡献指南

### 提交代码
1. Fork 项目仓库
2. 创建功能分支
3. 提交代码变更
4. 创建 Pull Request

### 文档更新
1. 更新相关文档
2. 确保示例代码正确
3. 更新变更日志

### 问题报告
1. 使用 Issue 模板
2. 提供详细的重现步骤
3. 包含环境信息

## 📞 联系方式

如有任何问题或建议，请通过以下方式联系：

- 项目 Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>
- 文档反馈: 直接在相关文档页面提出 Issue

---

**最后更新**: 2024-01-01  
**文档版本**: v1.0.0
