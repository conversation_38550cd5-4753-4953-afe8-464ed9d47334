# 部署文档

## 环境要求

### 开发环境
- Node.js >= 16.0.0
- pnpm >= 7.0.0
- TypeScript >= 5.2.2

### 生产环境
- Web 服务器 (Nginx/Apache)
- HTTPS 支持 (WPS SDK 要求)
- 现代浏览器支持

## 构建流程

### 1. 安装依赖

```bash
# 使用 pnpm 安装依赖
pnpm install

# 或使用 npm
npm install

# 或使用 yarn
yarn install
```

### 2. 环境配置

创建环境配置文件：

```bash
# .env.production
VITE_WPS_APP_ID=your_wps_app_id
VITE_WPS_ENDPOINT=https://o.wpsgo.com
VITE_API_BASE_URL=https://your-api-server.com
```

```bash
# .env.development
VITE_WPS_APP_ID=your_dev_wps_app_id
VITE_WPS_ENDPOINT=https://o.wpsgo.com
VITE_API_BASE_URL=http://localhost:8080
```

### 3. 构建项目

```bash
# 生产环境构建
pnpm build

# 开发环境构建
pnpm build:dev

# 构建并分析包大小
pnpm build --analyze
```

构建输出目录：`dist/`

## 部署方式

### 1. 静态文件部署

#### Nginx 配置

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL 证书配置
    ssl_certificate /path/to/your/cert.pem;
    ssl_certificate_key /path/to/your/key.pem;
    
    # 静态文件根目录
    root /var/www/daorigin_wps_ts/dist;
    index index.html;
    
    # 静态资源缓存
    location /static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # SPA 路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API 代理 (如果需要)
    location /api/ {
        proxy_pass http://your-backend-server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
}

# HTTP 重定向到 HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

#### Apache 配置

```apache
<VirtualHost *:443>
    ServerName your-domain.com
    DocumentRoot /var/www/daorigin_wps_ts/dist
    
    # SSL 配置
    SSLEngine on
    SSLCertificateFile /path/to/your/cert.pem
    SSLCertificateKeyFile /path/to/your/key.pem
    
    # 静态资源缓存
    <LocationMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
        Header append Cache-Control "public, immutable"
    </LocationMatch>
    
    # SPA 路由支持
    <Directory "/var/www/daorigin_wps_ts/dist">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
</VirtualHost>
```

### 2. Docker 部署

#### Dockerfile

```dockerfile
# 构建阶段
FROM node:18-alpine AS builder

WORKDIR /app

# 复制包管理文件
COPY package.json pnpm-lock.yaml ./

# 安装 pnpm
RUN npm install -g pnpm

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 构建项目
RUN pnpm build

# 生产阶段
FROM nginx:alpine

# 复制构建结果
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制 Nginx 配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 暴露端口
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

#### docker-compose.yml

```yaml
version: '3.8'

services:
  daorigin-wps:
    build: .
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    
  # 如果有后端服务
  api-server:
    image: your-api-server:latest
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=your_database_url
    restart: unless-stopped
```

#### 部署命令

```bash
# 构建镜像
docker build -t daorigin-wps:latest .

# 运行容器
docker run -d -p 80:80 -p 443:443 --name daorigin-wps daorigin-wps:latest

# 使用 docker-compose
docker-compose up -d
```

### 3. CDN 部署

#### 上传到 CDN

```bash
# 使用 AWS CLI 上传到 S3
aws s3 sync dist/ s3://your-bucket-name --delete

# 使用阿里云 OSS
ossutil cp -r dist/ oss://your-bucket-name/
```

#### CloudFront 配置 (AWS)

```json
{
  "Origins": [{
    "DomainName": "your-bucket.s3.amazonaws.com",
    "Id": "S3-your-bucket",
    "S3OriginConfig": {
      "OriginAccessIdentity": ""
    }
  }],
  "DefaultCacheBehavior": {
    "TargetOriginId": "S3-your-bucket",
    "ViewerProtocolPolicy": "redirect-to-https",
    "Compress": true,
    "CachePolicyId": "managed-caching-optimized"
  },
  "CustomErrorResponses": [{
    "ErrorCode": 404,
    "ResponseCode": 200,
    "ResponsePagePath": "/index.html"
  }]
}
```

## 环境变量配置

### 生产环境变量

```bash
# WPS 配置
VITE_WPS_APP_ID=prod_app_id
VITE_WPS_ENDPOINT=https://o.wpsgo.com

# API 配置
VITE_API_BASE_URL=https://api.your-domain.com
VITE_API_TIMEOUT=30000

# 功能开关
VITE_ENABLE_DEBUG=false
VITE_ENABLE_ANALYTICS=true

# 安全配置
VITE_CSP_NONCE=random_nonce_value
```

### 配置文件示例

```typescript
// src/config/index.ts
export const config = {
  wps: {
    appId: import.meta.env.VITE_WPS_APP_ID,
    endpoint: import.meta.env.VITE_WPS_ENDPOINT || 'https://o.wpsgo.com'
  },
  api: {
    baseUrl: import.meta.env.VITE_API_BASE_URL,
    timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 30000
  },
  features: {
    debug: import.meta.env.VITE_ENABLE_DEBUG === 'true',
    analytics: import.meta.env.VITE_ENABLE_ANALYTICS === 'true'
  }
};
```

## 性能优化

### 1. 构建优化

```typescript
// vite.config.ts
export default defineConfig({
  build: {
    // 代码分割
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['alpinejs', 'eventemitter3'],
          weboffice: ['./src/weboffice/WebOfficeSDKWrapper.ts']
        }
      }
    },
    // 压缩配置
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  }
});
```

### 2. 资源优化

```bash
# 图片压缩
npm install -g imagemin-cli
imagemin public/images/* --out-dir=dist/images

# CSS 优化
npm install -g cssnano-cli
cssnano src/style.css dist/style.min.css
```

### 3. 缓存策略

```nginx
# 静态资源长期缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# HTML 文件不缓存
location ~* \.html$ {
    expires -1;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
}
```

## 监控和日志

### 1. 错误监控

```typescript
// 集成 Sentry
import * as Sentry from "@sentry/browser";

Sentry.init({
  dsn: "your-sentry-dsn",
  environment: process.env.NODE_ENV
});
```

### 2. 性能监控

```typescript
// 性能指标收集
const observer = new PerformanceObserver((list) => {
  for (const entry of list.getEntries()) {
    // 发送性能数据到监控服务
    sendMetrics(entry);
  }
});

observer.observe({ entryTypes: ['navigation', 'paint', 'largest-contentful-paint'] });
```

### 3. 访问日志

```nginx
# Nginx 访问日志格式
log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                '$status $body_bytes_sent "$http_referer" '
                '"$http_user_agent" "$http_x_forwarded_for"';

access_log /var/log/nginx/access.log main;
```

## 安全配置

### 1. 内容安全策略 (CSP)

```html
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; 
               script-src 'self' 'unsafe-inline' https://o.wpsgo.com; 
               style-src 'self' 'unsafe-inline'; 
               img-src 'self' data: https:; 
               connect-src 'self' https://o.wpsgo.com;">
```

### 2. HTTPS 强制

```nginx
# 强制 HTTPS
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
```

### 3. 跨域配置

```typescript
// 如果需要配置 CORS
const corsOptions = {
  origin: ['https://your-domain.com', 'https://o.wpsgo.com'],
  credentials: true
};
```

## 故障排除

### 常见问题

1. **WPS SDK 加载失败**
   - 检查 HTTPS 配置
   - 验证 App ID 和 Token
   - 确认网络连接

2. **静态资源 404**
   - 检查构建输出路径
   - 验证服务器配置
   - 确认资源路径

3. **Alpine.js 不工作**
   - 检查 JavaScript 加载顺序
   - 验证组件注册
   - 查看浏览器控制台错误

### 日志分析

```bash
# 查看 Nginx 错误日志
tail -f /var/log/nginx/error.log

# 查看访问日志
tail -f /var/log/nginx/access.log

# 分析错误统计
awk '{print $9}' /var/log/nginx/access.log | sort | uniq -c | sort -nr
```
