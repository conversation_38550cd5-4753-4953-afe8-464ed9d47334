# API 文档

## WebOfficeSDKWrapper API

### 构造函数

```typescript
constructor(config: WebOfficeConfig)
```

**参数:**
- `config.url`: 文档 URL
- `config.token`: 访问令牌
- `config.mountId`: 挂载容器 ID
- `config.officeType`: 文档类型 ('word', 'excel', 'ppt')
- `config.refreshToken`: 刷新令牌回调函数
- `config.switchRevisionBtn`: 是否显示修订按钮

### 核心方法

#### init(): Promise<void>
初始化 WPS SDK 实例

```typescript
const wrapper = new WebOfficeSDKWrapper(config);
await wrapper.init();
```

#### ready(): Promise<any>
等待文档准备就绪

```typescript
await wrapper.ready();
console.log('文档已准备就绪');
```

#### destroy(): Promise<void>
销毁 WPS 实例

```typescript
await wrapper.destroy();
```

### 书签操作

#### getBookmarks(): Promise<BookmarkType[]>
获取所有书签

```typescript
const bookmarks = await wrapper.getBookmarks();
// 返回: [{ name: 'bookmark1', begin: 0, end: 10 }]
```

#### gotoBookmark(name: string): Promise<void>
跳转到指定书签

```typescript
await wrapper.gotoBookmark('bookmark1');
```

#### getBookmarkText(name: string): Promise<string>
获取书签内容

```typescript
const text = await wrapper.getBookmarkText('bookmark1');
```

#### setBookmarkText(name: string, text: string): Promise<void>
设置书签内容

```typescript
await wrapper.setBookmarkText('bookmark1', '新的内容');
```

### 评论操作

#### getComments(): Promise<any[]>
获取所有评论

```typescript
const comments = await wrapper.getComments();
```

#### addComment(text: string, position?: number): Promise<void>
添加评论

```typescript
await wrapper.addComment('这是一条评论', 100);
```

#### deleteComment(commentId: string): Promise<void>
删除评论

```typescript
await wrapper.deleteComment('comment-id');
```

### 修订操作

#### trackRevisions(enabled: boolean): boolean
启用/禁用修订模式

```typescript
wrapper.trackRevisions(true); // 启用修订
wrapper.trackRevisions(false); // 禁用修订
```

#### revisionsMode(mode: number): Promise<void>
设置修订显示模式

```typescript
await wrapper.revisionsMode(0); // 默认模式
await wrapper.revisionsMode(1); // 内联模式
```

#### setRevisionSetting(enabled: boolean): Promise<void>
设置修订按钮状态

```typescript
await wrapper.setRevisionSetting(true);
```

### 事件监听

#### on(event: string, callback: Function): void
监听事件

```typescript
wrapper.on('documentReady', () => {
    console.log('文档准备就绪');
});

wrapper.on('contentChanged', (data) => {
    console.log('内容已更改', data);
});

wrapper.on('bookmarkClicked', (bookmark) => {
    console.log('书签被点击', bookmark);
});
```

#### off(event: string, callback?: Function): void
取消事件监听

```typescript
wrapper.off('documentReady');
wrapper.off('contentChanged', specificCallback);
```



#### addLeases(): void
添加租赁详情

```typescript
financial.addLeases();
```

### Alpine.js 绑定方法

#### toggleContent(event: Event): void
切换内容展开/收起

```html
<div x-on:click="toggleContent($event)">
    点击展开/收起
</div>
```

#### scrollToSection(targetId: string): void
滚动到指定区域

```html
<button x-on:click="scrollToSection('basic-info')">
    跳转到基础信息
</button>
```

## ReviewDetailsComponent API

### 构造函数

```typescript
constructor(data: ReviewList)
```

### 核心方法

#### updateStatus(objectId: string, reviewData: Partial<ReviewData>): void
更新审核状态

```typescript
reviewDetail.updateStatus('obj-123', {
    status: 'approved',
    remark: '审核通过'
});
```

#### sendCustomEvent(eventName: string, data: any): void
发送自定义事件

```typescript
reviewDetail.sendCustomEvent('statusChanged', {
    objectId: 'obj-123',
    newStatus: 'approved'
});
```

### 筛选方法

#### filteredList(): ReviewData[]
获取过滤后的列表

```typescript
const filtered = reviewDetail.data.filteredList();
```

#### toggleRiskLevel(level: string): void
切换风险等级筛选

```typescript
reviewDetail.data.toggleRiskLevel('high');
```

#### getRiskCount(level: string): string | number
获取指定风险等级的数量

```typescript
const count = reviewDetail.data.getRiskCount('medium');
```

#### updateTypeFilter(type: string, label: string): void
更新类型筛选

```typescript
reviewDetail.data.updateTypeFilter('contract', '合同相关');
```

## LeasesComponent API

### 构造函数

```typescript
constructor(data: LeaseDetail | null)
```

### 核心方法

#### updateDataAndShow(newData: LeaseDetail): void
更新数据并显示

```typescript
leases.updateDataAndShow({
    objectId: 'lease-123',
    lease: {
        leaseCode: 'L001',
        useOfLeaseNo: '租金',
        // ... 其他属性
    }
});
```

#### validateForm(): boolean
验证租赁表单

```typescript
const isValid = leases.validateForm();
```

## 数据类型定义

### FinancialData

```typescript
interface FinancialData {
    finance: {
        buCode: string;           // 餐厅编号
        buName: string;           // 餐厅名称
        finMarket: string;        // 财务市场
        contractType: string;     // 合同类型
        strategicAlliance: string; // 战略联盟
        bizType: string;          // 业务类型
        partyA: string;           // 甲方
        addOfPartyA: string;      // 甲方地址
        partyB: string;           // 乙方
        addOfPartyB: string;      // 乙方地址
        addOfStore: string;       // 店铺地址
        signDate: string;         // 签署日期
        openDate: string;         // 开业日期
        // ... 更多属性
        leaseDetails: LeaseDetail[]; // 租赁详情列表
    };
}
```

### ReviewData

```typescript
interface ReviewData {
    objectId: string;         // 对象ID
    ruleType: string;         // 规则类型
    riskLevel: string;        // 风险等级
    relationType: number;     // 关联类型
    content: string;          // 内容
    additionalInfo: string;   // 附加信息
    status: string;           // 状态
    remark: string;           // 备注
    createTime: string;       // 创建时间
}
```

### LeaseDetail

```typescript
interface LeaseDetail {
    objectId: string;
    lease: {
        leaseCode: string;        // 租赁编码
        useOfLeaseNo: string;     // 租赁用途
        rentName: string;         // 租金名称
        rentType: string;         // 租金类型
        landlordCode: string;     // 房东编码
        landlordName: string;     // 房东名称
        // ... 更多属性
    };
}
```

## 错误处理

### 错误类型

```typescript
interface APIError {
    code: string;
    message: string;
    details?: any;
}
```

### 错误处理示例

```typescript
try {
    await wrapper.init();
} catch (error) {
    if (error.code === 'INVALID_TOKEN') {
        // 处理无效令牌
        await refreshToken();
    } else if (error.code === 'NETWORK_ERROR') {
        // 处理网络错误
        showNetworkErrorMessage();
    }
}
```

## 事件系统

### 全局事件

- `documentReady`: 文档准备就绪
- `contentChanged`: 内容已更改
- `bookmarkClicked`: 书签被点击
- `commentAdded`: 评论已添加
- `statusUpdated`: 状态已更新

### 事件数据格式

```typescript
interface DocumentReadyEvent {
    type: 'documentReady';
    timestamp: number;
}

interface ContentChangedEvent {
    type: 'contentChanged';
    changes: {
        type: 'insert' | 'delete' | 'modify';
        position: number;
        content: string;
    }[];
}
```
