import { EventEmitter } from 'eventemitter3';
import OpenSDK from "../lib/weboffice/open-jssdk-v0.0.13.es";

type BookmarkType = { name: string, begin: number, end: number };

interface WebOfficeConfig {
    url: string;
    token: string;
    mountId: string;
    officeType?:string;
    refreshToken?: Function;
    switchRevisionBtn?: boolean;
    wordOptions: { isShowDocMap:boolean };//word
}

interface ICommandBars {
    cmbId:string,
    attributes:
        {
            visible:boolean,
            enable:boolean
        }
}

/**
 * 9027 wps
 */
export default class WebOfficeSDKWrapper {
    private instance: any;
    private readonly officeType: string;
    private app: any;
    private readonly eventEmitter: EventEmitter;
    private lastClickCoordinates: { begin: number; end: number } | null = null;
    private lastClickTime: number = 0;
    private currentType: number = -1; // 默认为 -1（响应所有类型）
    private readonly switchRevisionBtn: boolean = true;
    private currentBookmarkIndex:number=0;
    private lastClickedBookmarks: BookmarkType[] = [];
    private fristOpenTime: number = 0;//文档打开时间
    /**
     * 构造函数
     * @param config 配置对象
     */
    constructor(config: WebOfficeConfig) {
        this.officeType = config.officeType ? config.officeType : 'w';
        this.switchRevisionBtn = config.switchRevisionBtn ?? true;
        this.eventEmitter = new EventEmitter();
        // 初始化实例配置
        this.initializeInstance(config);
    }

    /**
     * 初始化 WebOffice 实例
     * @param config 配置对象
     */
    private initializeInstance(config: WebOfficeConfig) {
        const { url, token, mountId, refreshToken ,wordOptions } = config;
        const instanceConfig: any = {
            url: url,
            mount: document.querySelector("#" + mountId) as HTMLElement
        };

        if (refreshToken) {
            instanceConfig.refreshToken = refreshToken;
        }

        if(wordOptions){
            instanceConfig.wordOptions = wordOptions;
        }else{
            instanceConfig.wordOptions = {
                isShowDocMap: false
            };
        }

        this.instance = OpenSDK.config(instanceConfig);

        if (token) {
            this.setToken(token);
        }
    }

    /**
     * 设置令牌
     * @param token 令牌
     */
    private setToken(token: string) {
        this.instance.setToken({ token, timeout: 10 * 60 * 1000 });
    }

    /**
     * 初始化方法
     */
    public async initialize() {
        this.showLoadingMessage();
        try {
            //注册监听文档打开
            this.instance.ApiEvent.AddApiEventListener("fileOpen", (data:any) => {
                if(data && data.success===true){
                    this.fristOpenTime= Date.now();
                }
            });
            await this.instance.ready();
            //注册监听文档点击改变
            if(this.officeType==='w'){
                this.instance.ApiEvent.AddApiEventListener('WindowSelectionChange', this.windowSelectionChangeHandle.bind(this));
            }
            this.app = this.instance.Application;
            //自动适应 9207 关闭， 自动适应 百胜 打开
            //this.app.ActiveDocument.ActiveWindow.View.Zoom.PageFit = 2;
            if(this.officeType==='w'){
                this.app.ActiveDocument.ActiveWindow.View.ShowComments = false;
                if (!this.switchRevisionBtn) {
                    const revisions = await this.app.ActiveDocument.Revisions;
                    await revisions.SwitchRevisionBtn(this.switchRevisionBtn);
                }
            }
            return this.app;
        } catch (error) {
            console.error('初始化过程中发生错误：', error);
            this.eventEmitter.emit('error', { message: '初始化过程中发生错误', error });
        } finally {
            this.hideLoadingMessage();
        }
    }

    /**
     * 设置当前类型并更新书签颜色
     * @param type 书签类型
     */
    public async setCurrentType(type: number) {
        this.currentType = type;
        const bookmarksList: BookmarkType[] = await this.app.ActiveDocument.Bookmarks.Json();
        for (const bookmark of bookmarksList) {
            if (bookmark.name.startsWith('qmZht_')) {
                const bookmarkType = this.getBookmarkType(bookmark.name);
                const range = await this.app.ActiveDocument.Range(bookmark.begin, bookmark.end);
                range.HighlightColorIndex = (bookmarkType === this.currentType || this.currentType === -1) ?
                    this.app.Enum.WdColorIndex.wdGreen : this.app.Enum.WdColorIndex.wdWhite;
            }
        }
    }

    /**
     * 监听消息
     * @param event 事件名称
     * @param listener 事件监听函数
     */
    public on(event: string, listener: (data: any) => void) {
        this.eventEmitter.on(event, listener);
    }

    /**
     * 插入文本
     * @param text 插入的文本
     */
    public async insertAfter(text: string): Promise<void> {
        await this.app.ActiveDocument.ActiveWindow.Selection.InsertAfter(text);
    }

    /**
     * 删除书签
     * @param bookmarkName 书签名称
     */
    public async deleteBookmark(bookmarkName: string) {
        await this.app.ActiveDocument.Bookmarks.Item(bookmarkName).Delete();
    }

    /**
     * 跳转到书签
     * @param bookmarkName 书签名称
     */
    public async goToBookmark(bookmarkName: string): Promise<void> {
        await this.performWithLoading(async () => {
            const bookmarksList: BookmarkType[] = await this.app.ActiveDocument.Bookmarks.Json();
            for (const bookmark of bookmarksList) {
                if (bookmark.name === bookmarkName) {
                    await this.app.ActiveDocument.ActiveWindow.Selection.GoTo({
                        What: this.app.Enum.WdGoToItem.wdGoToBookmark,
                        Which: this.app.Enum.WdGoToDirection.wdGoToAbsolute,
                        Name: bookmarkName
                    });
                    this.lastClickCoordinates = { begin: bookmark.begin, end: bookmark.end };
                    this.lastClickTime = Date.now();
                }
            }
        });
    }

    /**
     * 保存文档
     */
    public async save(){
        return await this.app.ActiveDocument.Save();
    }

    /**
     * 处理窗口选择变化事件
     * @param event 事件对象
     */
    private async windowSelectionChangeHandle(event: any) {
        const { begin, end } = event;
        const currentTime = Date.now();
        // 如果点击坐标相同且在1秒内，跳出函数
        if( (this.lastClickCoordinates &&
            begin >= this.lastClickCoordinates.begin &&
            end <= this.lastClickCoordinates.end &&
            currentTime - this.lastClickTime < 1000 ) ||currentTime - this.fristOpenTime < 1000) {
            return;
        }

        const bookmarksList: BookmarkType[] = await this.app.ActiveDocument.Bookmarks.Json();
        const matchingBookmarks = [];

        for (const bookmark of bookmarksList) {
            if (begin >= bookmark.begin && end <= bookmark.end) {
                const bookmarkType = this.getBookmarkType(bookmark.name);
                // 如果当前类型为 -1，或书签类型与当前类型一致
                if ((this.currentType === -1 || bookmarkType === this.currentType) && bookmark.name.startsWith('qmZht_')) {
                    matchingBookmarks.push({
                        name: bookmark.name,
                        begin: bookmark.begin,
                        end: bookmark.end
                    });
                }
            }
        }

        if (matchingBookmarks.length > 0) {
            // 如果当前书签列表与上次点击的书签列表不同，重置 currentBookmarkIndex
            if (!this.lastClickedBookmarks ||
                matchingBookmarks.length !== this.lastClickedBookmarks.length ||
                matchingBookmarks.some((bookmark, index) => bookmark.name !== this.lastClickedBookmarks[index].name)) {
                this.currentBookmarkIndex = 0;
            }

            const currentBookmark = matchingBookmarks[this.currentBookmarkIndex];
            this.eventEmitter.emit('bookmarkClicked', currentBookmark);

            this.currentBookmarkIndex = (this.currentBookmarkIndex + 1) % matchingBookmarks.length;
            this.lastClickCoordinates = { begin: currentBookmark.begin, end: currentBookmark.end };
            this.lastClickTime = currentTime;
            this.lastClickedBookmarks = matchingBookmarks; // 保存当前书签列表
        }
    }

    /**
     * 获取书签类型
     * @param bookmarkName 书签名称
     * @returns 书签类型
     */
    private getBookmarkType(bookmarkName: string): number {
        const matches = /^qmZht_(\d+)_/.exec(bookmarkName);
        return matches ? parseInt(matches[1], 10) : -1;
    }

    /**
     * 显示加载信息
     */
    private showLoadingMessage() {
        this.eventEmitter.emit('loading', { message: 'Loading...' });
    }

    /**
     * 隐藏加载信息
     */
    private hideLoadingMessage() {
        this.eventEmitter.emit('loadingCompleted', { message: 'Loading completed' });
    }

    /**
     * 带有加载信息的执行函数
     * @param asyncFunc 异步函数
     */
    private async performWithLoading(asyncFunc: () => Promise<void>) {
        let timeoutId: any;
        const loadingTimeout = new Promise<void>((_, reject) => {
            timeoutId = setTimeout(() => {
                this.showLoadingMessage();
                reject(new Error('Loading timeout'));
            }, 300);
            setTimeout(() => clearTimeout(timeoutId), 1000);
        });
        const asyncTask = asyncFunc().catch(error => {
            throw new Error(`操作失败，错误信息：${(error as Error).message}`);
        });
        try {
            await Promise.race([loadingTimeout, asyncTask]);
        } catch (error) {
            if ((error as Error).message === 'Loading timeout') {
                try {
                    await asyncTask;
                } finally {
                    this.hideLoadingMessage();
                }
            } else {
                throw error;
            }
        } finally {
            clearTimeout(timeoutId);
            this.hideLoadingMessage();
        }
    }

    /**
     * 动态更新组件状态
     * @param commandBars 命令栏数组
     */
    public setCommandBars(commandBars: ICommandBars[]) {
        this.instance.setCommandBars(commandBars);
    }

    /**
     * 设置修订按钮状态
     * @param enabled 是否启用
     */
    public async setRevisionSetting(enabled: boolean) {
        const ReviewTrackChanges = await this.app.CommandBars('ReviewTrackChanges');
        ReviewTrackChanges.Enabled = enabled;
    }

    /**
     * 编辑与修订模式切换
     * @param enabled Boolean 值为 true，则表明切换到修订模式，否则为编辑模式。
     */
    public trackRevisions(enabled: boolean) {
        return this.app.ActiveDocument.TrackRevisions = enabled;
    }

    /**
     * 以嵌入方式显示所有修订标识
     * @param mode 0: 默认方式、1: inline模式
     */
    public async  revisionsMode(mode:number){
        const revisions = await  this.app.ActiveDocument.Revisions;
        revisions.RevisionsMode = mode;
    }
}
