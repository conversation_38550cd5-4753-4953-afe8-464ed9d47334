import { EventEmitter } from 'eventemitter3';
import OpenSDK from "../lib/weboffice/open-jssdk-v0.0.13.es";
import { errorHandler, ErrorType } from '../lib/ErrorHandler';
import { MemoryManager } from '../lib/MemoryManager';
import { TIME_CONSTANTS, WPS_CONSTANTS } from '../lib/constants';

type BookmarkType = { name: string, begin: number, end: number };

interface WebOfficeConfig {
    url: string;
    token: string;
    mountId: string;
    officeType?: string;
    refreshToken?: () => void;
    switchRevisionBtn?: boolean;
}

interface ICommandBars {
    cmbId: string;
    attributes: {
        visible: boolean;
        enable: boolean;
    };
}

/***
 *  百胜 WebOfficeSDKWrapper 类
 */
// 定义 WPS SDK 相关类型
interface WPSInstance {
    ready(): Promise<void>;
    setToken(config: { token: string; timeout: number }): void;
    setCommandBars(commandBars: ICommandBars[]): void;
    ApiEvent: {
        AddApiEventListener(event: string, callback: (data: any) => void): void;
    };
    Application: any;
}

export default class WebOfficeSDKWrapper {
    private instance: any;
    private readonly officeType: string;
    private app: any;
    private readonly eventEmitter: EventEmitter;
    private lastClickCoordinates: { begin: number; end: number } | null = null;
    private lastClickTime: number = 0;
    private currentType: number = -1; // 默认为 -1（响应所有类型）
    private readonly switchRevisionBtn: boolean = true;
    private currentBookmarkIndex:number=0;
    private lastClickedBookmarks: BookmarkType[] = [];
    private currentCYBookmarkIndex:number=0; // 新增：CY书签索引
    private lastClickedCYBookmarks: BookmarkType[] = []; // 新增：上次点击的CY书签列表
    private fristOpenTime: number = 0;//文档打开时间
    /**
     * 构造函数
     * @param config 配置对象
     */
    constructor(config: WebOfficeConfig) {
        this.officeType = config.officeType || WPS_CONSTANTS.DEFAULT_OFFICE_TYPE;
        this.switchRevisionBtn = config.switchRevisionBtn ?? true;
        this.eventEmitter = new EventEmitter();
        // 初始化实例配置
        this.initializeInstance(config);
    }

    /**
     * 初始化 WebOffice 实例
     * @param config 配置对象
     */
    private initializeInstance(config: WebOfficeConfig) {
        const { url, token, mountId, refreshToken } = config;
        const instanceConfig: any = {
            url: url,
            mount: document.querySelector("#" + mountId) as HTMLElement
        };

        if (refreshToken) {
            instanceConfig.refreshToken = refreshToken;
        }

        this.instance = OpenSDK.config(instanceConfig) as any;

        if (token) {
            this.setToken(token);
        }
    }

    /**
     * 设置令牌
     * @param token 令牌
     */
    private setToken(token: string) {
        this.instance.setToken({ token, timeout: TIME_CONSTANTS.TOKEN_TIMEOUT });
    }

    /**
     * 初始化方法
     */
    public async initialize(): Promise<void> {
        this.showLoadingMessage();
        try {
            //注册监听文档打开
            this.instance.ApiEvent.AddApiEventListener("fileOpen", (data:any) => {
                if(data && data.success===true){
                    this.fristOpenTime= Date.now();
                }
            });
            await this.instance.ready();
            //注册监听文档点击改变
            if(this.officeType==='w'){
                this.instance.ApiEvent.AddApiEventListener('WindowSelectionChange', this.windowSelectionChangeHandle.bind(this));
            }
            this.app = this.instance.Application;
            //自动适应 9207 关闭， 自动适应 百胜 打开
            this.app.ActiveDocument.ActiveWindow.View.Zoom.PageFit = WPS_CONSTANTS.PAGE_FIT_WIDTH;
            if(this.officeType === WPS_CONSTANTS.DEFAULT_OFFICE_TYPE){
                this.app.ActiveDocument.ActiveWindow.View.ShowComments = WPS_CONSTANTS.SHOW_COMMENTS;
                if (!this.switchRevisionBtn) {
                    const revisions = await this.app.ActiveDocument.Revisions;
                    await revisions.SwitchRevisionBtn(this.switchRevisionBtn);
                }
            }

            // 使用 MemoryManager 安全地添加事件监听器
            MemoryManager.addEventListener(
                window,
                'blur',
                () => {
                    console.log('窗口失去焦点，可能切换回原窗口或最小化');
                    void this.tryAutoSave();
                },
                undefined,
                'weboffice-window-blur'
            );

            MemoryManager.addEventListener(
                document,
                'visibilitychange',
                () => {
                    if (document.visibilityState === 'hidden') {
                        console.log('页面不可见');
                        void this.tryAutoSave();
                    }
                },
                undefined,
                'weboffice-visibility-change'
            );

            return this.app;
        } catch (error) {
            console.error('初始化过程中发生错误：', error);
            this.eventEmitter.emit('error', { message: '初始化过程中发生错误', error });
        } finally {
            this.hideLoadingMessage();
        }
    }

    /**
     * 设置当前类型并更新书签颜色
     * @param type 书签类型
     */
    public async setCurrentType(type: number) {
        this.currentType = type;
        const bookmarksList: BookmarkType[] = await this.app.ActiveDocument.Bookmarks.Json();
        for (const bookmark of bookmarksList) {
            if (bookmark.name.startsWith('bk_bs_qaz_')) {
                const bookmarkType = this.getBookmarkType(bookmark.name);
                const range = await this.app.ActiveDocument.Range(bookmark.begin, bookmark.end);
                range.HighlightColorIndex = (bookmarkType === this.currentType || this.currentType === -1) ?
                    this.app.Enum.WdColorIndex.wdGreen : this.app.Enum.WdColorIndex.wdWhite;
            }
        }
    }

    /**
     * 监听消息
     * @param event 事件名称
     * @param listener 事件监听函数
     */
    public on(event: string, listener: (data: any) => void) {
        this.eventEmitter.on(event, listener);
    }

    /**
     * 插入文本
     * @param text 插入的文本
     */
    public async insertAfter(text: string): Promise<void> {
        await this.app.ActiveDocument.ActiveWindow.Selection.InsertAfter(text);
    }

    /**
     * 删除书签
     * @param bookmarkName 书签名称
     */
    public async deleteBookmark(bookmarkName: string) {
        await this.app.ActiveDocument.Bookmarks.Item(bookmarkName).Delete();
    }

    /**
     * 跳转到书签
     * @param bookmarkName 书签名称
     */
    public async goToBookmark(bookmarkName: string): Promise<void> {
        await this.performWithLoading(async () => {
            const bookmarksList: BookmarkType[] = await this.app.ActiveDocument.Bookmarks.Json();
            for (const bookmark of bookmarksList) {
                if (bookmark.name === bookmarkName) {
                    await this.app.ActiveDocument.ActiveWindow.Selection.GoTo({
                        What: this.app.Enum.WdGoToItem.wdGoToBookmark,
                        Which: this.app.Enum.WdGoToDirection.wdGoToAbsolute,
                        Name: bookmarkName
                    });
                    this.lastClickCoordinates = { begin: bookmark.begin, end: bookmark.end };
                    this.lastClickTime = Date.now();
                }
            }
        });
    }

    /**
     * 保存文档
     * @returns 保存状态对象，包含 result(String: 保存状态)、size(Number: 文件大小)、version(Number: 版本)
     */
    public async save(){
        return await this.app.ActiveDocument.Save();
    }

    /**
     * 尝试自动保存文档
     */
    private async tryAutoSave() {
        try {
            console.log('执行自动保存文档');
            const saveResult = await this.save();
            
            // 根据保存状态输出不同日志
            switch (saveResult.result) {
                case 'ok':
                    console.log('自动保存文档成功，版本已保存，可在历史版本中查看');
                    this.eventEmitter.emit('autoSaveSuccess', { message: '文档自动保存成功', size: saveResult.size, version: saveResult.version });
                    break;
                case 'nochange':
                    console.log('文档无更新，无需保存版本');
                    this.eventEmitter.emit('autoSaveNoChange', { message: '文档无更新，无需保存' });
                    break;
                case 'SavedEmptyFile':
                    console.warn('暂不支持保存空文件');
                    this.eventEmitter.emit('autoSaveWarning', { message: '暂不支持保存空文件' });
                    break;
                case 'SpaceFull':
                    console.error('空间已满，无法保存文档');
                    this.eventEmitter.emit('autoSaveError', { message: '空间已满，无法保存文档' });
                    break;
                case 'QueneFull':
                    console.warn('保存中请勿频繁操作，服务端处理保存队列已满，正在排队');
                    this.eventEmitter.emit('autoSaveWarning', { message: '保存队列已满，正在排队' });
                    break;
                case 'fail':
                    console.error('自动保存文档失败');
                    this.eventEmitter.emit('autoSaveError', { message: '自动保存文档失败' });
                    break;
                default:
                    console.log('自动保存文档，状态:', saveResult);
                    this.eventEmitter.emit('autoSaveStatus', { message: '自动保存状态', status: saveResult });
            }
        } catch (error) {
            console.error('自动保存文档失败：', error);
            this.eventEmitter.emit('error', { message: '自动保存文档失败', error });
        }
    }

    /**
     * 处理窗口选择变化事件
     * @param event 事件对象
     */
    private async windowSelectionChangeHandle(event: any) {
        const { begin, end } = event;
        const currentTime = Date.now();
        // 如果点击坐标相同且在防抖时间内，跳出函数
        if( (this.lastClickCoordinates &&
            begin >= this.lastClickCoordinates.begin &&
            end <= this.lastClickCoordinates.end &&
            currentTime - this.lastClickTime < TIME_CONSTANTS.CLICK_DEBOUNCE_TIME ) ||
            currentTime - this.fristOpenTime < TIME_CONSTANTS.DOCUMENT_INIT_TIME) {
            return;
        }

        const bookmarksList: BookmarkType[] = await this.app.ActiveDocument.Bookmarks.Json();
        const matchingBookmarks = [];
        const matchingCYBookmarks = []; // 新增：存储包含CY的书签 需求修改 2025-04-10 10:45:00

        for (const bookmark of bookmarksList) {
            if (begin >= bookmark.begin && end <= bookmark.end) {
                const bookmarkType = this.getBookmarkType(bookmark.name);
                
                // 如果当前类型为 -1，或书签类型与当前类型一致，且以bk_bs_qaz_开头，但不包含CY
                if ((this.currentType === -1 || bookmarkType === this.currentType) && 
                    bookmark.name.startsWith('bk_bs_qaz_') && 
                    !bookmark.name.includes('CY')) {
                    matchingBookmarks.push({
                        name: bookmark.name,
                        begin: bookmark.begin,
                        end: bookmark.end
                    });
                }
                
                // 修改：检查书签名以bk_bs_qaz_开头且包含CY
                if (bookmark.name.startsWith('bk_bs_qaz_') && bookmark.name.includes('CY')) {
                    matchingCYBookmarks.push({
                        name: bookmark.name,
                        begin: bookmark.begin,
                        end: bookmark.end
                    });
                }
            }
        }

        // 原有逻辑：处理普通书签
        if (matchingBookmarks.length > 0) {
            // 如果当前书签列表与上次点击的书签列表不同，重置 currentBookmarkIndex
            if (!this.lastClickedBookmarks ||
                matchingBookmarks.length !== this.lastClickedBookmarks.length ||
                matchingBookmarks.some((bookmark, index) => bookmark.name !== this.lastClickedBookmarks[index].name)) {
                this.currentBookmarkIndex = 0;
            }

            const currentBookmark = matchingBookmarks[this.currentBookmarkIndex];
            this.eventEmitter.emit('bookmarkClicked', currentBookmark);

            this.currentBookmarkIndex = (this.currentBookmarkIndex + 1) % matchingBookmarks.length;
            this.lastClickCoordinates = { begin: currentBookmark.begin, end: currentBookmark.end };
            this.lastClickTime = currentTime;
            this.lastClickedBookmarks = matchingBookmarks; // 保存当前书签列表
        }

        // 修改：处理包含CY的书签，添加循环发送机制
        if (matchingCYBookmarks.length > 0) {
            // 如果当前CY书签列表与上次点击的CY书签列表不同，重置 currentCYBookmarkIndex
            if (!this.lastClickedCYBookmarks ||
                matchingCYBookmarks.length !== this.lastClickedCYBookmarks.length ||
                matchingCYBookmarks.some((bookmark, index) => bookmark.name !== this.lastClickedCYBookmarks[index].name)) {
                this.currentCYBookmarkIndex = 0;
            }

            const currentCYBookmark = matchingCYBookmarks[this.currentCYBookmarkIndex];
            this.eventEmitter.emit('CYBookmarkClicked', currentCYBookmark);

            this.currentCYBookmarkIndex = (this.currentCYBookmarkIndex + 1) % matchingCYBookmarks.length;
            this.lastClickCoordinates = { begin: currentCYBookmark.begin, end: currentCYBookmark.end };
            this.lastClickTime = currentTime;
            this.lastClickedCYBookmarks = matchingCYBookmarks; // 保存当前CY书签列表
        }
    }

    /**
     * 获取书签类型
     * @param bookmarkName 书签名称
     * @returns 书签类型
     */
    private getBookmarkType(bookmarkName: string): number {
        const matches = /^bk_bs_qaz_(\d+)_/.exec(bookmarkName);
        return matches ? parseInt(matches[1], 10) : -1;
    }

    /**
     * 显示加载信息
     */
    private showLoadingMessage() {
        this.eventEmitter.emit('loading', { message: 'Loading...' });
    }

    /**
     * 隐藏加载信息
     */
    private hideLoadingMessage() {
        this.eventEmitter.emit('loadingCompleted', { message: 'Loading completed' });
    }

    /**
     * 带有加载信息的执行函数
     * @param asyncFunc 异步函数
     */
    private async performWithLoading(asyncFunc: () => Promise<void>) {
        let timeoutId: any;
        const loadingTimeout = new Promise<void>((_, reject) => {
            timeoutId = setTimeout(() => {
                this.showLoadingMessage();
                reject(new Error('Loading timeout'));
            }, 300);
            setTimeout(() => clearTimeout(timeoutId), 1000);
        });
        const asyncTask = asyncFunc().catch(error => {
            throw new Error(`操作失败，错误信息：${(error as Error).message}`);
        });
        try {
            await Promise.race([loadingTimeout, asyncTask]);
        } catch (error) {
            if ((error as Error).message === 'Loading timeout') {
                try {
                    await asyncTask;
                } finally {
                    this.hideLoadingMessage();
                }
            } else {
                throw error;
            }
        } finally {
            clearTimeout(timeoutId);
            this.hideLoadingMessage();
        }
    }

    /**
     * 动态更新组件状态
     * @param commandBars 命令栏数组
     */
    public setCommandBars(commandBars: ICommandBars[]) {
        this.instance.setCommandBars(commandBars);
    }

    /**
     * 设置修订按钮状态
     * @param enabled 是否启用
     */
    public async setRevisionSetting(enabled: boolean) {
        const ReviewTrackChanges = await this.app.CommandBars('ReviewTrackChanges');
        ReviewTrackChanges.Enabled = enabled;
    }

    /**
     * 编辑与修订模式切换
     * @param enabled Boolean 值为 true，则表明切换到修订模式，否则为编辑模式。
     */
    public trackRevisions(enabled: boolean) {
        return this.app.ActiveDocument.TrackRevisions = enabled;
    }

    /**
     * 以嵌入方式显示所有修订标识
     * @param mode 0: 默认方式、1: inline模式
     */
    public async  revisionsMode(mode:number){
        const revisions = await  this.app.ActiveDocument.Revisions;
        revisions.RevisionsMode = mode;
    }

    /**
     * 销毁实例，清理所有资源
     * 防止内存泄漏
     */
    public destroy(): void {
        try {
            // 清理事件监听器
            MemoryManager.removeEventListeners('weboffice-window-blur');
            MemoryManager.removeEventListeners('weboffice-visibility-change');

            // 清理 EventEmitter 的所有监听器
            this.eventEmitter.removeAllListeners();

            // 清理实例引用
            this.instance = null;
            this.app = null;

            // 重置状态
            this.lastClickCoordinates = null;
            this.lastClickTime = 0;
            this.currentBookmarkIndex = 0;
            this.lastClickedBookmarks = [];
            this.currentCYBookmarkIndex = 0;
            this.lastClickedCYBookmarks = [];
            this.fristOpenTime = 0;

            console.log('WebOfficeSDKWrapper 实例已销毁');
        } catch (error) {
            errorHandler.logError(
                ErrorType.WEBOFFICE_SDK,
                '销毁 WebOfficeSDKWrapper 实例时发生错误',
                error instanceof Error ? error : new Error(String(error))
            );
        }
    }
}
