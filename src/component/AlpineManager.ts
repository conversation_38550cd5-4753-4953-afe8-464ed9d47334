import Alpine from "alpinejs";

interface ComponentDefinition {
    name: string;
    definition: () => Record<string, any>;
}

export  class AlpineManager {
    // 静态标志，确保 Alpine 只启动一次
    private static isAlpineInitialized = false;

    // 静态队列，用于存储待注册的组件
    private static componentQueue: ComponentDefinition[] = [];

    /**
     * 启动 Alpine.js
     * @throws Error 如果 Alpine.js 初始化失败
     */
    public static start(): void {
        if (this.isAlpineInitialized) {
            console.warn('js 已经初始化过了');
            return;
        }

        try {
            // 注册队列中的所有组件
            this.componentQueue.forEach(({ name, definition }) => {
                Alpine.data(name, definition);
            });

            // 清空队列
            this.componentQueue = [];

            // 启动 Alpine.js
            Alpine.start();
            this.isAlpineInitialized = true;
            console.log("js 初始化成功");
        } catch (error) {
            console.error("js 初始化失败:", error);
            throw new Error("js 初始化失败");
        }
    }

    /**
     * 注册 Alpine 组件
     * @param name 组件名称
     * @param definition 组件定义
     * @throws Error 如果组件名称为空或组件定义无效
     */
    public static registerComponent(
        name: string, 
        definition: () => Record<string, any>
    ): void {
        // 验证参数
        if (!name?.trim()) {
            throw new Error("组件名称不能为空");
        }

        if (typeof definition !== 'function') {
            throw new Error("组件定义必须是一个函数");
        }

        try {
            if (this.isAlpineInitialized) {
                // 如果 Alpine 已初始化，直接注册组件
                Alpine.data(name, definition);
            } else {
                // 如果未初始化，将组件存入队列
                this.componentQueue.push({ name, definition });
            }
        } catch (error) {
            console.error(`注册组件 ${name} 失败:`, error);
            throw new Error(`注册组件 ${name} 失败`);
        }
    }

    /**
     * 检查组件是否已注册
     * @param name 组件名称
     */
    public static isComponentRegistered(name: string): boolean {
        if (this.isAlpineInitialized) {
            return typeof (Alpine as any).store?.[name] !== 'undefined';
        }
        return this.componentQueue.some(component => component.name === name);
    }
}
