/*
 * @Author: silverflute <EMAIL>
 * @Date: 2024-11-27 16:52:58
 * @LastEditors: silverflute <EMAIL>
 * @LastEditTime: 2024-12-27 11:04:39
 * @FilePath: /daorigin_wps_ts/src/component/DOMUtils.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * DOMUtils - 管理与 DOM 操作相关的实用函数
 */
export class DOMUtils {
    /**
     * 滚动到指定的元素
     * @param selectorOrEl 可以是元素的选择器字符串或 HTMLElement 实例
     * @param scrollOptions 可选的滚动参数，包含 behavior 和 block 属性
     * 此函数根据传入的参数判断是使用选择器查找元素还是直接操作传入的元素
     * 如果传入的是选择器字符串，使用 document.querySelector 查找元素
     * 如果传入的是 HTMLElement 实例，直接使用该元素进行滚动
     * 如果未找到元素或传入的元素无效，会在控制台输出警告信息并返回
     * 如果找到元素，将使用 scrollIntoView 方法将元素滚动到视口中央，滚动行为为平滑滚动
     */
    static scrollToElement(selectorOrEl: string | HTMLElement, scrollOptions: ScrollIntoViewOptions = { behavior: "smooth", block: "center" }): void {
        let element: HTMLElement | null = null;

        // 判断传入的参数类型
        if (typeof selectorOrEl === 'string') {
            element = document.querySelector(selectorOrEl);
        } else if (selectorOrEl instanceof HTMLElement) {
            element = selectorOrEl;
        }

        if (!element) {
            console.warn(`Element not found or invalid:`, selectorOrEl);
            return;
        }

        element.scrollIntoView(scrollOptions);
    }

    /**
     * 根据条件生成 HTML
     * @param condition 判断条件，当为 true 时，将内容包裹在 <span> 标签中，否则返回空字符串
     * @param content 要包裹在 <span> 标签中的内容
     * 此函数接收一个布尔类型的条件和一个字符串类型的内容作为参数
     * 若条件为真，将内容用 <span> 标签包裹并返回，若条件为假，返回空字符串
     */
    static generateHtml(condition: boolean, content: string): string {
        return condition? `<span>${content}</span>` : '';
    }
}