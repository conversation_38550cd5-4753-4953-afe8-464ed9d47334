/**
 * DOMUtils - 管理与 DOM 操作相关的实用函数
 * 提供安全的 DOM 操作和 HTML 生成功能
 */

import { SecurityUtils } from '../lib/SecurityUtils';
import { UI_CONSTANTS } from '../lib/constants';

export class DOMUtils {
    /**
     * 滚动到指定的元素
     * @param selectorOrEl 可以是元素的选择器字符串或 HTMLElement 实例
     * @param scrollOptions 可选的滚动参数，包含 behavior 和 block 属性
     */
    static scrollToElement(
        selectorOrEl: string | HTMLElement,
        scrollOptions: ScrollIntoViewOptions = {
            behavior: UI_CONSTANTS.DEFAULT_SCROLL_BEHAVIOR,
            block: UI_CONSTANTS.DEFAULT_SCROLL_BLOCK
        }
    ): void {
        let element: HTMLElement | null = null;

        // 判断传入的参数类型
        if (typeof selectorOrEl === 'string') {
            element = document.querySelector(selectorOrEl);
        } else if (selectorOrEl instanceof HTMLElement) {
            element = selectorOrEl;
        }

        if (!element) {
            console.warn(`Element not found or invalid:`, selectorOrEl);
            return;
        }

        element.scrollIntoView(scrollOptions);
    }

    /**
     * 安全地根据条件生成 HTML
     * @param condition 判断条件，当为 true 时，将内容包裹在指定标签中
     * @param content 要包裹的内容（会被自动转义）
     * @param tag 标签名称，默认为 'span'
     * @returns 安全的 HTML 字符串或空字符串
     */
    static generateHtml(condition: boolean, content: string, tag: string = 'span'): string {
        return SecurityUtils.generateSafeHtml(condition, content, tag);
    }

    /**
     * 安全地设置元素内容
     * @param element 目标元素
     * @param content 要设置的内容
     * @param useTextContent 是否使用 textContent（推荐），默认为 true
     */
    static safeSetContent(element: HTMLElement, content: string, useTextContent: boolean = true): void {
        SecurityUtils.safeSetContent(element, content, useTextContent);
    }
}