import Alpine from "alpinejs";
import { DOMUtils } from "./DOMUtils";
/**
 * 附件接口
 * @interface Attachment
 */
export interface Attachment {
    objectId: string; // 打开窗口
    name: string; // 附件名称
    fileType:string;//附件类型
}

/**
 * 备注类型枚举
 */
export enum RemarkType {
    Read = 1,        // 阅读
    Ignore = 2,      // 忽略
    Revoke = 3,      // 撤销
    Remark = 4,      // 备注
}
/**
 * 备注对象
 */
export interface Remark {
    userName:string;//用户名
    type:RemarkType;//1:阅读 2:忽略 3:撤销 4:备注
    remark:string;// 备注描述
    createdAt:number;//创建时间 毫秒数
}


/**
 * 关联类型枚举
 */
export enum RelationType {
    ALL=0,//全部
    ClauseOnly = 1,      // 条款有关
    AttachmentOnly = 2,  // 附件有关
    Both = 3,            // 条款与附件都有关
}

/**
 * 风险等级枚举
 */
export enum RiskLevel {
    Low = "1",        // 高风险
    Medium = "2",     // 中风险
    High = "3",       // 低风险
}
/**
 * 规则类型枚举
 */
export enum RuleType {
    ALL="",//全部
    Legal = "FW",   // 法务
    Financial = "CW", // 财务
    Business = "YW",  // 开发
    ContractDiff = "CY", // 合同差异
}
/**
 * 审核数据接口
 * @interface ReviewData
 */
export interface ReviewData {
    objectId: string; // 主键id
    ruleType: RuleType; // 规则类型：FW是法务;CW是财务;YW是业务；CY合同差异
    isRisk: boolean; // 有无风险
    atts: Attachment[]; // 相关附件
    relationType: RelationType; // 关联类型 1 条款有关 2 与附件有关  3 与条款附件都有关
    riskLevel: RiskLevel; // 当前等级 1 2 3
    additionalInfo: string; // 附加信息
    riskMessage: string; // 条款名称
    riskCause: string; // 提示原因
    semanticDiffList: string[]; // 语义差异
    ignore:boolean;//是否忽略
    read:boolean;//是否已读
    remarks:Remark[];//备注数组
    code:string;// 条款code
    source:string;//风险来源
    riskAnchor:string;//风险锚点
    anchorSubsection:string;//章节锚点
    // UI 交互
    showRemarkInput: boolean; // 是否显示备注输入框 默认 false
    newRemarkContent: string; // 当前备注输入内容 默认空
    remarkStatus: RemarkType; // 记录点击备注按钮的来源状态 默认 Ignore
    showGiR:boolean;//控制显示点赞和不点赞 默认 false
}

// 提取类型定义
type Option<T> = { label: string; value: T };

/**
 * 审核列表接口
 * @interface ReviewList
 */
export interface ReviewList {
    list: Array<ReviewData>;
    isRemark:number;//能不能备注  1 为 true 为是 0 为否
    debug:number;//是否调试模式 1 为 true 为是 0 为否
    //筛选的条件
    selectedItemId: string; // 当前选中的 id
    currentRuleType: string; // 当前选择类型
    currentRiskLevel: string[]; // 支持数字和 "noRisk"
    currentRelationType: number; // 关联类型 0 全部  1 条款有关 2 与附件有关 3 与条款附件都有关
    selectedTypeLabel:string;//关联类型 名称
    currentSearchText: string; // 搜索文本
    currentRemarks: Remark[];// 当前窗口的备注数组
    attObjectId: string; // 当前选中的附件 objectId
    //筛选条件结束
    filteredList: () => ReviewData[];// 过滤后的列表
    toggleRiskLevel: (level: string ) => void;
    getRiskCount: (level: string) => string | number;
    getRiskClass: (item: ReviewData)=> string;
    getAdditionalInfoClass: (additionalInfo: string)=> string;
    updateTypeFilter: (type:string, label:string) => void;//刷选类型
    getRemarkType:(type:number)=>string;//获取类型名称
    getPlaceholderText:(type:number)=>string;//获取类型名称 给输入框用
    getTitleHtml:(item: ReviewData)=>string;//获取标题的html
    getTypeText:(type:number)=>string;//获取类型名称
    formatDate:(timestamp:number)=>string;//获取时间
    //
    onItemClick: (item: ReviewData) => void;
    onAttachmentClick: (objectId: string,attachment:Attachment) => void;
    onRejectClick: (item: ReviewData) => void;
    onRemarkClick: (item: ReviewData) => void;
    onLikeClick: (objectId: string)=>void;
    onDislikeClick: (objectId: string)=>void;
    onReopenClick: (item: ReviewData)=>void;
    submitRemark: (item: ReviewData)=>void;
    resetRemark: (item: ReviewData)=>void;
    onShowRemarks: (objectId: string,remarks: Remark[],event: Event )=>void;
    //UI
    relationTypeOptions: {label:string,value:number}[],
    ruleTypeOptions: {label:string,value:string}[],
}

/***
 * 审核详情组件类，用于展示审核详情信息
 *
 * @class ReviewDetail
 * @constructor
 * @param {ReviewData} data
 **/
export class ReviewDetail {
    public data: ReviewList;
    // 静态标志，用于确保 Alpine 只初始化一次
    private static isAlpineInitialized:boolean = false;
    private previousFilters: Partial<ReviewList> | null = null; // 用于保存筛选条件

    constructor(data: Omit<ReviewList, "onItemClick">) {
        const _this = this;
        // 为每个 ReviewData 补充默认值
        const enrichedList = data.list.map((item) => ({
            ...item, // 保留原有属性
            showRemarkInput: false, // 默认值：不显示备注输入框
            newRemarkContent: '', // 默认值：备注内容为空
            remarkStatus: RemarkType.Ignore, // 默认值：备注状态为 Ignore
            showGiR: false // 默认值：不显示点赞/点踩
        }));
        this.data = Alpine.reactive({
            ...data,
            list: enrichedList, // 替换原来的 list 为补充过的列表
            selectedItemId: '',
            currentRuleType: '',
            currentRiskLevel: ["1","2","3"],
            currentRemarks:[],
            currentRelationType: 0,
            currentSearchText: '',
            relationTypeOptions: [
                { label: '全部', value: RelationType.ALL },
                { label: '条款有关', value: RelationType.ClauseOnly },
                { label: '附件有关', value: RelationType.AttachmentOnly },
                { label: '条款与附件都有关', value: RelationType.Both },
            ],
            //规则类型：FW是法务;CW是财务;YW是业务；CY合同差异
            ruleTypeOptions: [
                { label: '全部', value: '' },
                { label: '法务', value: RuleType.Legal },
                { label: '财务', value: RuleType.Financial },
                { label: '开发', value: RuleType.Business },
            ],
            // 点击条目事件
            onItemClick(item: ReviewData) {
                _this.sendCustomEvent('item-click', { item });
                _this.data.selectedItemId = item.objectId;
            },
            // 点赞事件
            onLikeClick(objectId: string) {
                _this.sendCustomEvent('like-click', { objectId });
                _this.data.selectedItemId = objectId;
            },
            // 点踩事件
            onDislikeClick(objectId: string) {
                _this.sendCustomEvent('dislike-click', { objectId });
                _this.data.selectedItemId = objectId;
            },
            // 点击忽略事件
            onRejectClick(item: ReviewData) {
                _this.data.selectedItemId = item.objectId;
                _this.sendCustomEvent('reject-click', { item });
                if(_this.data.isRemark===0)return;
                _this.toggleRemarkInput(item, RemarkType.Ignore);
            },
            // 点击重新开启事件
            onReopenClick(item: ReviewData) {
                _this.data.selectedItemId = item.objectId;
                _this.sendCustomEvent('reopen-click', { item });
                if(_this.data.isRemark===0)return;
                _this.toggleRemarkInput(item, RemarkType.Revoke);
            },
            // 点击备注事件
            onRemarkClick(item: ReviewData) {
                _this.data.selectedItemId = item.objectId;
                _this.sendCustomEvent('remark-click', { item });
                if(_this.data.isRemark===0)return;
                _this.toggleRemarkInput(item, RemarkType.Remark);
            },
            // 提交备注事件
            submitRemark(item: ReviewData) {
                _this.data.selectedItemId = item.objectId;
                if (item.newRemarkContent.trim()) {
                    _this.sendCustomEvent('submit-remark', { newRemarkContent: item.newRemarkContent, remarkType: item.remarkStatus,objectId:item.objectId });
                    _this.resetRemarkInput(item);
                }
            },
            resetRemark(item: ReviewData) {
                _this.data.selectedItemId = item.objectId;
                _this.resetRemarkInput(item);
            },
            onShowRemarks(objectId: string,remarks: Remark[],event: Event ){
                _this.data.selectedItemId = objectId;
                _this.data.currentRemarks=remarks;
                // 获取点击的 DOM 元素
                const clickedElement = event.currentTarget as HTMLElement;
                const liElement = clickedElement.closest('li');
                _this.sendCustomEvent('show-remark-click', { remarks ,liElement });
            },
            // 点击附件事件
            onAttachmentClick(objectId: string,attachment: Attachment) {
                _this.data.selectedItemId = objectId;
                _this.sendCustomEvent('attachment-click', { attachment });
            },
            //ui
            toggleRiskLevel(level: string) {
                const index = this.currentRiskLevel.indexOf(level);
                if (index === -1) {
                    this.currentRiskLevel.push(level);
                } else {
                    this.currentRiskLevel.splice(index, 1);
                }
            },
            getRiskCount(level: string):string {
                let count;
                if (level === 'noRisk') {
                    count = this.list.filter(item => !item.isRisk).length;
                } else {
                    count = this.list.filter(item => item.riskLevel === level).length;
                }
                return count > 999 ? '999+' : count.toString();
            },
            getRiskClass(item: ReviewData): string {
                if (!item.isRisk) return 'color-4';
                switch (item.riskLevel) {
                    case "1": return 'color-1';
                    case "2": return 'color-2';
                    case "3": return 'color-3';
                    default: return 'color-4';
                }
            },
            getTitleHtml(item: ReviewData): string {
                return DOMUtils.generateHtml(item.ignore, '已忽略') + item.riskMessage;
            },
            // 格式化时间
            formatDate(timestamp:number) {
                const date = new Date(timestamp);
                return date.toLocaleString();
            },
            /**
             * 根据附加信息里是否包含'未报备'，'已报备' 字符串返回 className
             * @param additionalInfo
             */
            getAdditionalInfoClass(additionalInfo: string): string {
                if (additionalInfo.includes('未报备')) {
                    return 'not-reported';
                } else if (additionalInfo.includes('已报备')) {
                    return 'reported';
                } else {
                    return '';
                }
            },
            /**
             * 获取备注类型名称
             * @param type
             */
            getRemarkType(type: RemarkType): string {
                switch(type){
                    case RemarkType.Read:
                        return '已读';
                    case RemarkType.Ignore:
                        return '忽略';
                    case RemarkType.Revoke:
                        return '撤销';
                    case RemarkType.Remark:
                        return '备注';
                }
            },
            getPlaceholderText(type: RemarkType): string {
                switch(type){
                    case RemarkType.Read:
                        return '';
                    case RemarkType.Ignore:
                        return '请填写忽略理由';
                    case RemarkType.Revoke:
                        return '请填写撤回理由';
                    case RemarkType.Remark:
                        return '请填写备注';
                }
            },
            // 动态过滤列表，根据筛选条件实时更新显示的内容
            filteredList(): ReviewData[] {
                const searchText = this.currentSearchText.toLowerCase();
                // 通用文本匹配函数
                const matchesText = (field: string | string[]) =>
                    Array.isArray(field)
                        ? field.some(item => item?.toLowerCase().includes(searchText))
                        : field?.toLowerCase().includes(searchText) || false;

                const filtered = this.list.filter(item => {
                    // 如果风险等级没有选中任何一项，直接返回空（不展示条目）
                    if (this.currentRiskLevel.length === 0) return false;

                    // 附件 objectId 筛选逻辑
                    if (this.attObjectId) {
                        const matchesAttachment = item.atts.some(att => att.objectId === this.attObjectId);
                        if (!matchesAttachment) {
                            return false;
                        }
                    }

                    // 如果选中了风险等级但条目不匹配任何选中等级，则过滤掉
                    const isRiskMatched = item.isRisk && this.currentRiskLevel.includes(item.riskLevel);
                    // 如果选中了 "noRisk" 且条目是无风险的，则匹配
                    const isNoRiskMatched = !item.isRisk && (this.currentRiskLevel as (number | string)[]).includes("noRisk");
                    // 条目需要符合风险等级或无风险的匹配条件之一
                    if (!isRiskMatched && !isNoRiskMatched) {
                        return false;
                    }


                    // 筛选规则类型
                    if (this.currentRuleType && item.ruleType !== this.currentRuleType) {
                        return false;
                    }


                    // 筛选关联类型
                    if (this.currentRelationType !== 0 && item.relationType !== this.currentRelationType) {
                        return false;
                    }

                    // 搜索关键字匹配逻辑
                    if (this.currentSearchText) {
                        const matchesSearch =
                            matchesText(item.additionalInfo) ||
                            matchesText(item.riskMessage) ||
                            matchesText(item.riskCause) ||
                            matchesText(item.semanticDiffList) ||
                            matchesText(item.atts.map(att => att.name));

                        if (!matchesSearch) {
                            return false;
                        }
                    }

                    // 如果是调试模式，额外匹配 riskAnchor 和 anchorSubsection
                    if (this.debug === 1) {
                        const matchesRiskAnchor =
                            matchesText(item.riskAnchor) || matchesText(item.anchorSubsection);
                        if (!matchesRiskAnchor) {
                            return false;
                        }
                    }

                    // 如果所有条件都通过，保留该项
                    return true;
                });
                // 在数据更新后，利用 queueMicrotask 来确保下次事件循环执行
                queueMicrotask(() => {
                    // 确保 Alpine.js 的 DOM 更新已经完成
                    Alpine.nextTick(() => {
                        _this.sendCustomEvent('filteredList-over', { count: filtered.length });
                    });
                });
                return filtered;
            },
            //刷选类型
            updateTypeFilter(type:string, label:string) {
                this.currentRuleType=type;
                this.selectedTypeLabel = label;
            }
        });

        this.registerComponent();
        if (!ReviewDetail.isAlpineInitialized) {
            Alpine.start();
            ReviewDetail.isAlpineInitialized = true;
            console.log("js 已初始化");
        }
    }

    /**
     * 注册组件
     * @private
     */
    private registerComponent() {
        Alpine.data("reviewDetailComponent", () => this.data);
    }

    /**
     * 发送自定义事件
     * @param eventName 事件名称
     * @param detail 事件携带的参数
     */
    private sendCustomEvent(eventName: string, detail: any) {
        const event = new CustomEvent(eventName, { detail });
        window.dispatchEvent(event);
    }

    /**
     * 切换备注输入框显示状态
     * @param item 当前条目
     * @param status 备注状态
     */
    private toggleRemarkInput(item: ReviewData, status: RemarkType) {
        item.showRemarkInput = true;
        item.remarkStatus = status;
        this.data.list.forEach(i => {
            i.newRemarkContent = '';
            i.showRemarkInput = i.objectId === item.objectId;
        });
    }

    /**
     * 重置备注输入框
     * @param item 当前条目
     */
    private resetRemarkInput(item: ReviewData) {
        item.newRemarkContent = '';
        item.showRemarkInput = false;
        item.remarkStatus = RemarkType.Read;
    }

    /**
     * 接收外部消息，更新内部数据并触发 DOM 更新
     * @param objectId 要更新的对象ID
     * @param reviewData 要更新的数据
     */
    public updateStatus(objectId: string, reviewData: Partial<ReviewData>) {
        console.log(`Updating status for ${objectId} with data:`, reviewData);
        // 找到对应的条目
        const item:ReviewData | undefined=  this.data.list.find(item => item.objectId === objectId);

        if (item) {
            // 使用 Object.assign 更新对应条目，只更新指定的字段
            Object.assign(item, reviewData);
            console.log(`Updated item:`, item);
            // 触发 Alpine.js 的反应式机制，重新渲染界面
            //this.data.filteredList();
        } else {
            console.warn(`Item with objectId ${objectId} not found.`);
        }
    }

    /**
     * 根据附件 objectId 筛选数据，并保存当前筛选条件
     * @param attachmentId 附件 objectId
     */
    public filterByAttachment(attachmentId: string) {
        this.previousFilters = {
            currentRuleType: this.data.currentRuleType,
            currentRiskLevel: [...this.data.currentRiskLevel],
            currentRelationType: this.data.currentRelationType,
            currentSearchText: this.data.currentSearchText,
            attObjectId: this.data.attObjectId
        };
        this.data.currentSearchText = '';
        this.data.currentRuleType='';
        this.data.currentRelationType=0;
        this.data.currentRiskLevel = ["1", "2", "3"];
        this.data.attObjectId = attachmentId;
        this.data.filteredList();
    }

    /**
     * 恢复之前保存的筛选条件，并重新渲染数据
     */
    /**
     * 恢复之前的筛选条件
     * 此函数将当前的筛选条件恢复为之前存储的筛选条件
     * 它会从 previousFilters 中获取之前存储的各个筛选条件的值，并将其赋值给相应的属性
     * 包括 currentRuleType、currentRiskLevel、currentRelationType、currentSearchText 和 attObjectId
     * 最后调用 filteredList 方法更新过滤后的列表
     */
    public restorePreviousFilter() {
        if(this.previousFilters){
            this.data.currentRuleType = this.previousFilters.currentRuleType ?? '';
            this.data.currentRiskLevel = [...(this.previousFilters.currentRiskLevel ||  ["1", "2", "3"])];
            this.data.currentRelationType = this.previousFilters.currentRelationType ?? 0;
            this.data.currentSearchText = this.previousFilters.currentSearchText ?? '';
            this.data.attObjectId = this.previousFilters.attObjectId?? '';
        }
        this.data.filteredList();
    }

    /**
     * 全部已读改变所有item read 状态
     */
    public allReadChange(){
        this.data.list.forEach(item=>{
            item.read = true;
        });
    }
    /**
     * 滚动到指定条目
     * @param objectId 要滚动到的条目的唯一标识符
     * 此函数用于将页面滚动到具有指定 objectId 的元素位置
     * 首先将 data 对象的 selectedItemId 属性设置为传入的 objectId
     * 然后根据 objectId 构造一个 CSS 选择器，用于查找相应元素
     * 最后调用 DOMUtils 中的 scrollToElement 方法将页面滚动到该元素位置
     */
    public scrollToItem(objectId:string) {
        // 查找对应的元素
        this.data.selectedItemId = objectId;
        const selector = `li[data-object-id="${objectId}"]`;
        DOMUtils.scrollToElement(selector);
    }
}
