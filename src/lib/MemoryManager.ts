/**
 * 内存管理工具类
 * 提供事件监听器管理、资源清理等内存管理功能
 */

export interface EventListenerInfo {
    element: EventTarget;
    type: string;
    listener: EventListener;
    options?: boolean | AddEventListenerOptions;
}

export class MemoryManager {
    private static eventListeners = new Map<string, EventListenerInfo[]>();
    private static timers = new Map<string, NodeJS.Timeout>();
    private static intervals = new Map<string, NodeJS.Timeout>();
    private static observers = new Map<string, MutationObserver | IntersectionObserver | ResizeObserver>();

    /**
     * 安全地添加事件监听器
     * @param element 目标元素
     * @param type 事件类型
     * @param listener 监听器函数
     * @param options 选项
     * @param key 唯一标识符，用于后续清理
     */
    static addEventListener(
        element: EventTarget,
        type: string,
        listener: EventListener,
        options?: boolean | AddEventListenerOptions,
        key: string = 'default'
    ): void {
        // 添加事件监听器
        element.addEventListener(type, listener, options);

        // 记录监听器信息
        if (!this.eventListeners.has(key)) {
            this.eventListeners.set(key, []);
        }

        this.eventListeners.get(key)!.push({
            element,
            type,
            listener,
            options
        });
    }

    /**
     * 移除指定 key 的所有事件监听器
     * @param key 标识符
     */
    static removeEventListeners(key: string): void {
        const listeners = this.eventListeners.get(key);
        if (!listeners) return;

        listeners.forEach(({ element, type, listener, options }) => {
            element.removeEventListener(type, listener, options);
        });

        this.eventListeners.delete(key);
    }

    /**
     * 移除所有事件监听器
     */
    static removeAllEventListeners(): void {
        for (const key of this.eventListeners.keys()) {
            this.removeEventListeners(key);
        }
    }

    /**
     * 安全地设置定时器
     * @param callback 回调函数
     * @param delay 延迟时间
     * @param key 唯一标识符
     * @returns 定时器 ID
     */
    static setTimeout(callback: () => void, delay: number, key: string): NodeJS.Timeout {
        // 清理已存在的定时器
        this.clearTimeout(key);

        const timer = setTimeout(() => {
            callback();
            this.timers.delete(key);
        }, delay);

        this.timers.set(key, timer);
        return timer;
    }

    /**
     * 清理指定的定时器
     * @param key 标识符
     */
    static clearTimeout(key: string): void {
        const timer = this.timers.get(key);
        if (timer) {
            clearTimeout(timer);
            this.timers.delete(key);
        }
    }

    /**
     * 安全地设置间隔定时器
     * @param callback 回调函数
     * @param interval 间隔时间
     * @param key 唯一标识符
     * @returns 定时器 ID
     */
    static setInterval(callback: () => void, interval: number, key: string): NodeJS.Timeout {
        // 清理已存在的间隔定时器
        this.clearInterval(key);

        const timer = setInterval(callback, interval);
        this.intervals.set(key, timer);
        return timer;
    }

    /**
     * 清理指定的间隔定时器
     * @param key 标识符
     */
    static clearInterval(key: string): void {
        const timer = this.intervals.get(key);
        if (timer) {
            clearInterval(timer);
            this.intervals.delete(key);
        }
    }

    /**
     * 安全地创建 MutationObserver
     * @param callback 回调函数
     * @param key 唯一标识符
     * @returns MutationObserver 实例
     */
    static createMutationObserver(
        callback: MutationCallback,
        key: string
    ): MutationObserver {
        // 清理已存在的观察器
        this.disconnectObserver(key);

        const observer = new MutationObserver(callback);
        this.observers.set(key, observer);
        return observer;
    }

    /**
     * 安全地创建 IntersectionObserver
     * @param callback 回调函数
     * @param options 选项
     * @param key 唯一标识符
     * @returns IntersectionObserver 实例
     */
    static createIntersectionObserver(
        callback: IntersectionObserverCallback,
        options?: IntersectionObserverInit,
        key: string = 'intersection'
    ): IntersectionObserver {
        // 清理已存在的观察器
        this.disconnectObserver(key);

        const observer = new IntersectionObserver(callback, options);
        this.observers.set(key, observer);
        return observer;
    }

    /**
     * 断开指定的观察器
     * @param key 标识符
     */
    static disconnectObserver(key: string): void {
        const observer = this.observers.get(key);
        if (observer) {
            observer.disconnect();
            this.observers.delete(key);
        }
    }

    /**
     * 清理所有资源
     */
    static cleanup(): void {
        // 清理事件监听器
        this.removeAllEventListeners();

        // 清理定时器
        for (const timer of this.timers.values()) {
            clearTimeout(timer);
        }
        this.timers.clear();

        // 清理间隔定时器
        for (const timer of this.intervals.values()) {
            clearInterval(timer);
        }
        this.intervals.clear();

        // 清理观察器
        for (const observer of this.observers.values()) {
            observer.disconnect();
        }
        this.observers.clear();
    }

    /**
     * 获取当前资源使用情况
     * @returns 资源统计信息
     */
    static getResourceStats(): {
        eventListeners: number;
        timers: number;
        intervals: number;
        observers: number;
    } {
        let totalListeners = 0;
        for (const listeners of this.eventListeners.values()) {
            totalListeners += listeners.length;
        }

        return {
            eventListeners: totalListeners,
            timers: this.timers.size,
            intervals: this.intervals.size,
            observers: this.observers.size
        };
    }
}
