/**
 * 性能优化工具类
 * 提供防抖、节流、缓存等性能优化功能
 */

import { PERFORMANCE_CONSTANTS } from './constants';

export class PerformanceUtils {
    private static debounceTimers = new Map<string, NodeJS.Timeout>();
    private static throttleTimers = new Map<string, { lastCall: number; timer?: NodeJS.Timeout }>();
    private static cache = new Map<string, { value: any; timestamp: number; ttl: number }>();

    /**
     * 防抖函数
     * @param func 要防抖的函数
     * @param delay 延迟时间，默认使用配置值
     * @param key 唯一标识符，用于区分不同的防抖函数
     * @returns 防抖后的函数
     */
    static debounce<T extends (...args: any[]) => any>(
        func: T,
        delay: number = PERFORMANCE_CONSTANTS.DEBOUNCE_DELAY,
        key: string = func.name || 'default'
    ): (...args: Parameters<T>) => void {
        return (...args: Parameters<T>) => {
            // 清除之前的定时器
            const existingTimer = this.debounceTimers.get(key);
            if (existingTimer) {
                clearTimeout(existingTimer);
            }

            // 设置新的定时器
            const timer = setTimeout(() => {
                func.apply(null, args);
                this.debounceTimers.delete(key);
            }, delay);

            this.debounceTimers.set(key, timer);
        };
    }

    /**
     * 节流函数
     * @param func 要节流的函数
     * @param interval 节流间隔，默认使用配置值
     * @param key 唯一标识符
     * @returns 节流后的函数
     */
    static throttle<T extends (...args: any[]) => any>(
        func: T,
        interval: number = PERFORMANCE_CONSTANTS.THROTTLE_INTERVAL,
        key: string = func.name || 'default'
    ): (...args: Parameters<T>) => void {
        return (...args: Parameters<T>) => {
            const now = Date.now();
            const throttleInfo = this.throttleTimers.get(key);

            if (!throttleInfo) {
                // 第一次调用
                func.apply(null, args);
                this.throttleTimers.set(key, { lastCall: now });
                return;
            }

            const timeSinceLastCall = now - throttleInfo.lastCall;

            if (timeSinceLastCall >= interval) {
                // 可以执行
                func.apply(null, args);
                throttleInfo.lastCall = now;
            } else {
                // 需要等待
                if (throttleInfo.timer) {
                    clearTimeout(throttleInfo.timer);
                }

                throttleInfo.timer = setTimeout(() => {
                    func.apply(null, args);
                    throttleInfo.lastCall = Date.now();
                    delete throttleInfo.timer;
                }, interval - timeSinceLastCall);
            }
        };
    }

    /**
     * 缓存函数结果
     * @param func 要缓存的函数
     * @param ttl 缓存生存时间（毫秒），默认5分钟
     * @param keyGenerator 缓存键生成函数
     * @returns 带缓存的函数
     */
    static memoize<T extends (...args: any[]) => any>(
        func: T,
        ttl: number = 5 * 60 * 1000, // 5分钟
        keyGenerator?: (...args: Parameters<T>) => string
    ): T {
        return ((...args: Parameters<T>) => {
            const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);
            const cached = this.cache.get(key);
            const now = Date.now();

            // 检查缓存是否有效
            if (cached && (now - cached.timestamp) < cached.ttl) {
                return cached.value;
            }

            // 执行函数并缓存结果
            const result = func.apply(null, args);
            this.cache.set(key, {
                value: result,
                timestamp: now,
                ttl
            });

            return result;
        }) as T;
    }

    /**
     * 清理过期缓存
     */
    static cleanExpiredCache(): void {
        const now = Date.now();
        for (const [key, cached] of this.cache.entries()) {
            if ((now - cached.timestamp) >= cached.ttl) {
                this.cache.delete(key);
            }
        }
    }

    /**
     * 清理所有定时器和缓存
     */
    static cleanup(): void {
        // 清理防抖定时器
        for (const timer of this.debounceTimers.values()) {
            clearTimeout(timer);
        }
        this.debounceTimers.clear();

        // 清理节流定时器
        for (const throttleInfo of this.throttleTimers.values()) {
            if (throttleInfo.timer) {
                clearTimeout(throttleInfo.timer);
            }
        }
        this.throttleTimers.clear();

        // 清理缓存
        this.cache.clear();
    }

    /**
     * 批量处理数组，避免阻塞主线程
     * @param items 要处理的数组
     * @param processor 处理函数
     * @param batchSize 批次大小
     * @returns Promise
     */
    static async processBatch<T, R>(
        items: T[],
        processor: (item: T, index: number) => R | Promise<R>,
        batchSize: number = 100
    ): Promise<R[]> {
        const results: R[] = [];
        
        for (let i = 0; i < items.length; i += batchSize) {
            const batch = items.slice(i, i + batchSize);
            const batchResults = await Promise.all(
                batch.map((item, index) => processor(item, i + index))
            );
            results.push(...batchResults);

            // 让出控制权，避免阻塞
            if (i + batchSize < items.length) {
                await new Promise(resolve => setTimeout(resolve, 0));
            }
        }

        return results;
    }
}
