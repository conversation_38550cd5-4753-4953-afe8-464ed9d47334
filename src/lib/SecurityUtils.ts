/**
 * 安全工具类
 * 提供 XSS 防护、输入验证等安全功能
 */

import { SECURITY_CONSTANTS } from './constants';

export class SecurityUtils {
    /**
     * HTML 转义，防止 XSS 攻击
     * @param text 需要转义的文本
     * @returns 转义后的安全文本
     */
    static escapeHtml(text: string): string {
        if (typeof text !== 'string') {
            return String(text);
        }

        return text.replace(/[&<>"'/]/g, (match) => {
            return SECURITY_CONSTANTS.HTML_ESCAPE_MAP[match as keyof typeof SECURITY_CONSTANTS.HTML_ESCAPE_MAP] || match;
        });
    }

    /**
     * 安全地生成 HTML 内容
     * @param condition 判断条件
     * @param content 要包裹的内容
     * @param tag 标签名称，默认为 'span'
     * @returns 安全的 HTML 字符串
     */
    static generateSafeHtml(condition: boolean, content: string, tag: string = 'span'): string {
        if (!condition) {
            return '';
        }

        // 验证标签是否在允许列表中
        if (!SECURITY_CONSTANTS.ALLOWED_HTML_TAGS.includes(tag as any)) {
            tag = 'span'; // 默认使用 span 标签
        }

        const escapedContent = this.escapeHtml(content);
        return `<${tag}>${escapedContent}</${tag}>`;
    }

    /**
     * 验证输入字符串
     * @param input 输入字符串
     * @param maxLength 最大长度
     * @param allowEmpty 是否允许空值
     * @returns 验证结果
     */
    static validateInput(input: string, maxLength: number = 1000, allowEmpty: boolean = true): {
        isValid: boolean;
        error?: string;
        sanitized: string;
    } {
        if (typeof input !== 'string') {
            return {
                isValid: false,
                error: '输入必须是字符串',
                sanitized: ''
            };
        }

        const trimmed = input.trim();

        if (!allowEmpty && trimmed.length === 0) {
            return {
                isValid: false,
                error: '输入不能为空',
                sanitized: trimmed
            };
        }

        if (trimmed.length > maxLength) {
            return {
                isValid: false,
                error: `输入长度不能超过 ${maxLength} 个字符`,
                sanitized: trimmed.substring(0, maxLength)
            };
        }

        return {
            isValid: true,
            sanitized: trimmed
        };
    }

    /**
     * 清理 URL，防止恶意链接
     * @param url 输入的 URL
     * @returns 清理后的安全 URL
     */
    static sanitizeUrl(url: string): string {
        if (typeof url !== 'string') {
            return '';
        }

        const trimmed = url.trim();
        
        // 检查是否是安全的协议
        const safeProtocols = ['http:', 'https:', 'mailto:', 'tel:'];
        const hasProtocol = /^[a-zA-Z][a-zA-Z0-9+.-]*:/.test(trimmed);
        
        if (hasProtocol) {
            const protocol = trimmed.split(':')[0].toLowerCase() + ':';
            if (!safeProtocols.includes(protocol)) {
                return ''; // 不安全的协议，返回空字符串
            }
        }

        return trimmed;
    }

    /**
     * 防止 DOM 型 XSS 攻击的安全 DOM 操作
     * @param element DOM 元素
     * @param content 要设置的内容
     * @param useTextContent 是否使用 textContent 而不是 innerHTML
     */
    static safeSetContent(element: HTMLElement, content: string, useTextContent: boolean = true): void {
        if (!element || typeof content !== 'string') {
            return;
        }

        if (useTextContent) {
            element.textContent = content;
        } else {
            // 如果必须使用 innerHTML，先转义内容
            element.innerHTML = this.escapeHtml(content);
        }
    }

    /**
     * 安全地获取元素属性
     * @param element DOM 元素
     * @param attribute 属性名
     * @returns 属性值或空字符串
     */
    static safeGetAttribute(element: Element, attribute: string): string {
        if (!element || typeof attribute !== 'string') {
            return '';
        }

        const value = element.getAttribute(attribute);
        return value ? this.escapeHtml(value) : '';
    }
}
