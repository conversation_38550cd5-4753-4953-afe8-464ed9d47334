/**
 * 统一错误处理工具类
 * 提供应用级别的错误处理和日志记录功能
 */

import { ERROR_CONSTANTS } from './constants';

export enum ErrorType {
    INITIALIZATION = 'INITIALIZATION',
    WEBOFFICE_SDK = 'WEBOFFICE_SDK',
    COMPONENT = 'COMPONENT',
    NETWORK = 'NETWORK',
    VALIDATION = 'VALIDATION',
    UNKNOWN = 'UNKNOWN'
}

export interface AppError {
    type: ErrorType;
    message: string;
    originalError?: Error;
    timestamp: number;
    context?: Record<string, any>;
}

export class ErrorHandler {
    private static instance: ErrorHandler;
    private errorLog: AppError[] = [];
    private maxLogSize = ERROR_CONSTANTS.MAX_LOG_SIZE;

    private constructor() {}

    public static getInstance(): ErrorHandler {
        if (!ErrorHandler.instance) {
            ErrorHandler.instance = new ErrorHandler();
        }
        return ErrorHandler.instance;
    }

    /**
     * 记录错误
     */
    public logError(
        type: ErrorType,
        message: string,
        originalError?: Error,
        context?: Record<string, any>
    ): AppError {
        const error: AppError = {
            type,
            message,
            originalError,
            timestamp: Date.now(),
            context
        };

        this.errorLog.push(error);
        
        // 保持日志大小限制
        if (this.errorLog.length > this.maxLogSize) {
            this.errorLog.shift();
        }

        // 控制台输出
        console.error(`[${type}] ${message}`, {
            originalError,
            context,
            timestamp: new Date(error.timestamp).toISOString()
        });

        return error;
    }

    /**
     * 处理异步操作错误
     */
    public async handleAsync<T>(
        operation: () => Promise<T>,
        errorType: ErrorType,
        errorMessage: string,
        context?: Record<string, any>
    ): Promise<T | null> {
        try {
            return await operation();
        } catch (error) {
            this.logError(
                errorType,
                errorMessage,
                error instanceof Error ? error : new Error(String(error)),
                context
            );
            return null;
        }
    }

    /**
     * 获取错误日志
     */
    public getErrorLog(): AppError[] {
        return [...this.errorLog];
    }

    /**
     * 清空错误日志
     */
    public clearErrorLog(): void {
        this.errorLog = [];
    }

    /**
     * 获取特定类型的错误
     */
    public getErrorsByType(type: ErrorType): AppError[] {
        return this.errorLog.filter(error => error.type === type);
    }
}

// 导出单例实例
export const errorHandler = ErrorHandler.getInstance();
