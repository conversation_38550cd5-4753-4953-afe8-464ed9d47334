export interface ITokenData {
  token: string;
  timeout: number;
}

export interface IConfig {
  mount?: HTMLElement;
  url: string;
  refreshToken?: () => ITokenData | Promise<ITokenData>;
  setToken?: ITokenData;
  widgetData?: Array<IWidgetInfo>;
}

export  interface OpenSDK {
  config: (config: IConfig) => Promise<any>;
  setToken: (token: ITokenData) => void;
}

declare const OpenSDK: OpenSDK;

export default OpenSDK;
