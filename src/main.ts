/**
 * 应用程序入口文件
 * 负责初始化和暴露全局组件
 */

// WebOffice SDK 封装器
import WebOfficeSDKWrapper from "./weboffice/WebOfficeSDKWrapper.ts";

// 合同审核详情组件
import { ReviewDetail } from "./component/ReviewDetailsComponent.ts";

// 工具类
import { MemoryManager } from "./lib/MemoryManager";
import { PerformanceUtils } from "./lib/PerformanceUtils";

/**
 * 全局组件注册
 * 将组件暴露到 window 对象供外部调用
 */
declare global {
    interface Window {
        WebOfficeSDKWrapper: typeof WebOfficeSDKWrapper;
        ReviewDetail: typeof ReviewDetail;
        MemoryManager: typeof MemoryManager;
        PerformanceUtils: typeof PerformanceUtils;
        // 全局清理函数
        cleanupResources: () => void;
    }
}

try {
    // 注册 WebOffice SDK 封装器
    window.WebOfficeSDKWrapper = WebOfficeSDKWrapper;

    // 注册合同审核详情组件
    window.ReviewDetail = ReviewDetail;

    // 注册工具类
    window.MemoryManager = MemoryManager;
    window.PerformanceUtils = PerformanceUtils;

    // 注册全局清理函数
    window.cleanupResources = () => {
        MemoryManager.cleanup();
        PerformanceUtils.cleanup();
        console.log('全局资源已清理');
    };

    // 页面卸载时自动清理资源
    MemoryManager.addEventListener(
        window,
        'beforeunload',
        () => {
            window.cleanupResources();
        },
        undefined,
        'global-cleanup'
    );

    console.log('应用组件初始化完成');
} catch (error) {
    console.error('应用组件初始化失败:', error);
    throw new Error('应用初始化失败');
}


