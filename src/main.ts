/**
 * 应用程序入口文件
 * 负责初始化和暴露全局组件
 */

// WebOffice SDK 封装器
import WebOfficeSDKWrapper from "./weboffice/WebOfficeSDKWrapper.ts";

// 合同审核详情组件
import { ReviewDetail } from "./component/ReviewDetailsComponent.ts";

/**
 * 全局组件注册
 * 将组件暴露到 window 对象供外部调用
 */
declare global {
    interface Window {
        WebOfficeSDKWrapper: typeof WebOfficeSDKWrapper;
        ReviewDetail: typeof ReviewDetail;
    }
}

try {
    // 注册 WebOffice SDK 封装器
    window.WebOfficeSDKWrapper = WebOfficeSDKWrapper;

    // 注册合同审核详情组件
    window.ReviewDetail = ReviewDetail;

    console.log('应用组件初始化完成');
} catch (error) {
    console.error('应用组件初始化失败:', error);
    throw new Error('应用初始化失败');
}


