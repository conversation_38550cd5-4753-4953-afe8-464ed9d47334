---
description: 
globs: 
alwaysApply: false
---
# Project Structure Guide

This is a TypeScript-based web project using Vite as the build tool. Here's the key structure:

## Core Files
- [vite.config.ts](mdc:vite.config.ts) - Vite configuration file
- [tsconfig.json](mdc:tsconfig.json) - TypeScript configuration
- [package.json](mdc:package.json) - Project dependencies and scripts
- [index.html](mdc:index.html) - Main HTML entry point

## Source Code Structure
The `src` directory contains the main application code:
- [src/main.ts](mdc:src/main.ts) - Application entry point
- [src/style.css](mdc:src/style.css) - Global styles

### Key Directories
- `src/weboffice/` - WebOffice integration components
- `src/component/` - Reusable UI components
- `src/types/` - TypeScript type definitions
- `src/ui/` - UI-specific components
- `src/lib/` - Utility functions and libraries

## Build Output
- `dist/` - Production build output
- `public/` - Static assets served as-is
