---
description: 
globs: 
alwaysApply: false
---
# Coding Conventions

## TypeScript Guidelines
- Use TypeScript's strict mode
- Define explicit types for function parameters and return values
- Use interfaces for object shapes
- Prefer type inference where types are obvious

## Component Structure
- One component per file
- Use functional components with hooks
- Follow the naming pattern: `ComponentName.tsx`
- Place related components in appropriate subdirectories

## Styling
- CSS files should be co-located with their components
- Use semantic class names
- Follow BEM naming convention for CSS classes

## File Organization
- Group related files together
- Keep components small and focused
- Place shared utilities in `src/lib`
- Define shared types in `src/types`

## Best Practices
- Write self-documenting code
- Add JSDoc comments for complex functions
- Use early returns to reduce nesting
- Keep functions pure when possible
- Use meaningful variable names
- Avoid magic numbers and strings

## Error Handling
- Use TypeScript's error handling features
- Provide meaningful error messages
- Handle edge cases explicitly
- Log errors appropriately

## Performance
- Memoize expensive computations
- Use proper React hooks dependencies
- Avoid unnecessary re-renders
- Lazy load components when appropriate
