---
description: 
globs: 
alwaysApply: false
---
# WebOffice Integration Guide

## Overview
The WebOffice integration is handled in the [src/weboffice](mdc:src/weboffice) directory. This module provides integration with WPS Office functionality.

## Key Components
- WebOffice initialization and configuration
- Document handling and manipulation
- Event listeners and callbacks
- UI integration components

## Best Practices
- Initialize WebOffice components early in the application lifecycle
- Handle document state changes properly
- Implement proper error boundaries around WebOffice components
- Cache document state appropriately
- Use TypeScript interfaces for WebOffice API types

## Error Handling
- Implement proper error handling for WebOffice operations
- Provide user-friendly error messages
- Handle network-related issues gracefully
- Log WebOffice-specific errors for debugging

## Performance Considerations
- Optimize document loading and saving
- Minimize unnecessary re-renders of WebOffice components
- Handle large documents efficiently
- Use appropriate caching strategies
